import Image from 'next/image';
import DropdownMenuItem from './components/DropdownMenuItem';
import { getUserProfile } from './actions/profile';
import { UserProfile } from '@/types/profile';
import UserAvatar from './components/UserAvatar';
import {
  PencilIcon,
  PresentationChartBarIcon,
  UserCircleIcon,
  UsersIcon,
} from '@heroicons/react/24/outline';
import MobileNavigation from '@/clients/views/common/MobileNavigation';
import NavCollapse from '@/clients/views/common/NavCollapse';
import Link from 'next/link';

const BASE_URL = process.env.NEXT_PUBLIC_BASE_URL || '';

const Header = async () => {
  const data = await getUserProfile<UserProfile>();

  return (
    <header className="fixed top-0 z-[99] bg-[#092a35] w-full">
      <div className="fluid-container h-[64px] md:h-[72px] flex-center-between py-2">
        <Image
          alt="cnc logo"
          src={`/images/cnc-logo.svg`}
          width="0"
          height="0"
          sizes="100vw"
          className="w-[auto] h-7 md:h-[36px] hidden md:block"
          priority
        />
        <Image
          alt="cnc logo"
          src={`/images/cnc-logo-mobile.svg`}
          width="0"
          height="0"
          sizes="100vw"
          className="w-[auto] h-7 md:h-[36px] block md:hidden"
          priority
        />
        <div className="hidden md:flex items-center gap-8">
          <a href={`${BASE_URL}/contacts`} className="text-[#8BABB6] text-sm">
            Contacts
          </a>
          <DropdownMenuItem title="Rentals">
            <li>
              <a href={`${BASE_URL}/properties`} className="rounded-none">
                Property Listings
              </a>
            </li>
            <li>
              <a href={`${BASE_URL}/leases`} className="rounded-none">
                Leases
              </a>
            </li>
            <li>
              <a href={`${BASE_URL}/lease-report`} className="rounded-none">
                Payment Report
              </a>
            </li>
            <li>
              <Link href={`/leads`} className="rounded-none">
                Leads
              </Link>
            </li>
          </DropdownMenuItem>
          <DropdownMenuItem title="Resources">
            <li>
              <a
                href="https://live.activepipe.com/dashboard"
                target="_blank"
                className="rounded-none"
              >
                ActivePipe
              </a>
            </li>
            <li>
              <a
                href="https://app.docusign.com/home"
                target="_blank"
                className="rounded-none"
              >
                Docusign
              </a>
            </li>
            <li>
              <a
                href="https://nantucketma.mapgeo.io/"
                target="_blank"
                className="rounded-none"
              >
                GIS Maps
              </a>
            </li>
            <li>
              <a
                href="https://records.nantucket-ma.gov/WebLink/Browse.aspx?id=145009&dbid=0&repo=TownofNantucket"
                target="_blank"
                className="rounded-none"
              >
                Health Department
              </a>
            </li>
            <li>
              <a
                href="http://nantucket.mylinkmls.com/"
                target="_blank"
                className="rounded-none"
              >
                LINK
              </a>
            </li>
            <li>
              <a
                href="https://www.masslandrecords.com/"
                target="_blank"
                className="rounded-none"
              >
                Mass Land Records
              </a>
            </li>
            <li>
              <a
                href="http://my.moxiworks.com/"
                target="_blank"
                className="rounded-none"
              >
                MoxiWorks
              </a>
            </li>
            <li>
              <a href={`/dashboard/lead-assignment`} className="rounded-none">
                Lead Assignment Dashboard
              </a>
            </li>
          </DropdownMenuItem>
          {data?.is_admin && (
            <DropdownMenuItem title="Admin">
              <li>
                <a href={`${BASE_URL}/schedule`} className="rounded-none">
                  Schedule
                </a>

                <a
                  href={`https://wordpress.congdonandcoleman.com/wp-admin/`}
                  target="_blank"
                  className="rounded-none"
                >
                  Blog
                </a>

                <a
                  href={`${BASE_URL}/congdonand-coleman-agents`}
                  className="rounded-none"
                >
                  Agents
                </a>

                <a
                  href={`${BASE_URL}/tools-resources`}
                  className="rounded-none"
                >
                  Tools & Resources
                </a>
              </li>
            </DropdownMenuItem>
          )}
        </div>
        <MobileNavigation>
          <>
            <a
              href={`${BASE_URL}/contacts`}
              className="text-black w-full p-2 mb-2 font-medium flex gap-2"
            >
              <UsersIcon className="w-5 h-5" />
              Contacts
            </a>
            <NavCollapse
              name="Rentals"
              title={
                <div className="text-black w-full font-medium flex gap-2">
                  <PresentationChartBarIcon className="w-5 h-5" />
                  Rentals
                </div>
              }
            >
              <a
                href={`${BASE_URL}/properties`}
                className="rounded-none px-4 py-2 text-sm"
              >
                Property Listings
              </a>
              <a
                href={`${BASE_URL}/leases`}
                className="rounded-none px-4 py-2 text-sm"
              >
                Leases
              </a>
              <a
                href={`${BASE_URL}/lease-report`}
                className="rounded-none px-4 py-2 text-sm"
              >
                Payment Report
              </a>
              <Link href={`/leads`} className="rounded-none px-4 py-2 text-sm">
                Leads
              </Link>
            </NavCollapse>
            <NavCollapse
              name="Admin"
              title={
                <div className="text-black w-full font-medium flex gap-2">
                  <UserCircleIcon className="w-5 h-5" />
                  Admin
                </div>
              }
            >
              <a
                href={`${BASE_URL}/schedule`}
                className="rounded-none px-4 py-2 text-sm"
              >
                Schedule
              </a>
              <a
                href={`https://wordpress.congdonandcoleman.com/wp-admin/`}
                target="_blank"
                className="rounded-none px-4 py-2 text-sm"
              >
                Blog
              </a>

              <a
                href={`${BASE_URL}/congdonand-coleman-agents`}
                className="rounded-none px-4 py-2 text-sm"
              >
                Agents
              </a>

              <a
                href={`${BASE_URL}/tools-resources`}
                className="rounded-none px-4 py-2 text-sm"
              >
                Tools & Resources
              </a>
              <a
                href={`/dashboard/lead-assignment`}
                className="rounded-none px-4 py-2 text-sm"
              >
                Lead Assignment Dashboard
              </a>
            </NavCollapse>
            <NavCollapse
              name="Resources"
              title={
                <div className="text-black w-full font-medium flex gap-2">
                  <PencilIcon className="w-5 h-5" />
                  Resources
                </div>
              }
            >
              <a
                href="https://live.activepipe.com/dashboard"
                target="_blank"
                className="rounded-none px-4 py-2 text-sm"
              >
                ActivePipe
              </a>

              <a
                href="https://app.docusign.com/home"
                target="_blank"
                className="rounded-none px-4 py-2 text-sm"
              >
                Docusign
              </a>

              <a
                href="https://nantucketma.mapgeo.io/"
                target="_blank"
                className="rounded-none px-4 py-2 text-sm"
              >
                GIS Maps
              </a>

              <a
                href="https://records.nantucket-ma.gov/WebLink/Browse.aspx?id=145009&dbid=0&repo=TownofNantucket"
                target="_blank"
                className="rounded-none px-4 py-2 text-sm"
              >
                Health Department
              </a>

              <a
                href="http://nantucket.mylinkmls.com/"
                target="_blank"
                className="rounded-none px-4 py-2 text-sm"
              >
                LINK
              </a>

              <a
                href="https://www.masslandrecords.com/"
                target="_blank"
                className="rounded-none px-4 py-2 text-sm"
              >
                Mass Land Records
              </a>

              <a
                href="http://my.moxiworks.com/"
                target="_blank"
                className="rounded-none px-4 py-2 text-sm"
              >
                MoxiWorks
              </a>
            </NavCollapse>
          </>
        </MobileNavigation>

        {data && (
          <div className="hidden md:block">
            <UserAvatar profile={data} />
          </div>
        )}
      </div>
    </header>
  );
};

export default Header;
