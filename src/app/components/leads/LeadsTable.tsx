import TimezoneFormatTimestamp from '@/clients/components/common/ClientFormatTimestamp';
import ExpandableText from '@/clients/views/common/ExpandableText';
import LeadStatusSelect from '@/clients/views/leads/LeadStatusSelect';
import SortableTableHeader from '@/clients/views/leads/SortableTableHeader';
import { Lead } from '@/types/leads';
import {
  formatPhoneNumber,
  getFullName,
  parseDateString,
} from '@/utils/common';
import { getLeadFlexibilityString } from '@/utils/leads';
import classNames from 'classnames';
import { format } from 'date-fns';
import Link from 'next/link';

const BASE_URL = process.env.NEXT_PUBLIC_BASE_URL || '';

type Props = {
  leads: Lead[];
};

const LeadsTable = ({ leads }: Props) => {
  return (
    <div className="p-4 border rounded-lg shadow-sm bg-white my-4">
      <div className="overflow-x-auto">
        <table className="table">
          <thead className="bg-slate-50 rounded-t-md">
            <tr>
              <th className="text-sm text-slate-600 font-medium leading-[20px]">
                Status
              </th>
              <SortableTableHeader
                title="Lead Date"
                className="text-sm text-slate-600 font-medium leading-[20px]"
                queryKey="ordering"
                queryValue="created_at"
              />

              <th className="text-sm text-slate-600 font-medium leading-[20px]">
                Name
              </th>
              <th className="text-sm text-slate-600 font-medium leading-[20px]">
                Agent
              </th>
              <th className="text-sm text-slate-600 font-medium leading-[20px]">
                Dates
              </th>
              <th className="text-sm text-slate-600 font-medium leading-[20px]">
                Property/Area
              </th>
              <th className="text-sm text-slate-600 font-medium leading-[20px]">
                Comments
              </th>
            </tr>
          </thead>
          <tbody>
            {leads.map((_lead, index) => (
              <tr key={index} className="">
                <td className="align-top">
                  <LeadStatusSelect status={_lead.status} leadId={_lead.id} />
                </td>
                <td className="align-top">
                  <p className="text-xs text-slate-500">
                    <TimezoneFormatTimestamp
                      timestamp={_lead.created_at}
                      format="MM/DD/YYYY"
                    />
                  </p>
                </td>
                <td className="align-top">
                  <a
                    href={`${BASE_URL}/contact/${_lead.contact.contact_id}`}
                    className="text-sm font-medium"
                  >
                    {getFullName(
                      _lead.contact.first_name,
                      _lead.contact.last_name
                    )}
                  </a>
                  <p className="text-xs text-slate-500">
                    {_lead.contact.email1}
                  </p>
                  <p className="text-xs text-slate-500">
                    {formatPhoneNumber(_lead.contact.cell_phone ?? '')}
                  </p>
                </td>
                <td className="align-top">
                  <p className="text-sm text-slate-700">
                    {_lead?.user?.first_name ?? ''}
                  </p>
                </td>
                <td className="align-top">
                  <p className="text-sm text-slate-700">
                    {_lead.arrival_date &&
                      format(
                        parseDateString(_lead.arrival_date),
                        'eee MMM d'
                      )}{' '}
                    -{' '}
                    {_lead.departure_date &&
                      format(
                        parseDateString(_lead.departure_date),
                        'eee MMM d, yyyy'
                      )}
                  </p>
                  {_lead?.flexibility && (
                    <div
                      className={classNames(
                        'rounded-md py-1 px-2 w-max text-xs leading-4',
                        {
                          'bg-yellow-50 text-yellow-700 ring-1 ring-yellow-200':
                            _lead.flexibility === 'flexible_dates',
                          'bg-emerald-50 text-emerald-700 ring-1 ring-emerald-200':
                            _lead.flexibility === 'uncertain',
                          'bg-rose-50 text-rose-700 ring-1 ring-rose-200':
                            _lead.flexibility === 'exact_dates',
                        }
                      )}
                    >
                      {getLeadFlexibilityString(_lead.flexibility)}
                    </div>
                  )}
                </td>
                <td className="min-w-[160px] align-top">
                  {_lead?.listing && (
                    <Link
                      href={`/property/${_lead.listing}/location`}
                      className="text-sm leading-5 text-carolina-blue underline"
                    >
                      {_lead?.address ?? ''}
                    </Link>
                  )}
                </td>
                <td className="min-w-[160px] md:w-[240px] align-top">
                  <ExpandableText
                    text={_lead?.note ?? ''}
                    className="text-sm leading-5 text-slate-600"
                    limitLength={45}
                  />
                </td>
              </tr>
            ))}
          </tbody>
        </table>
      </div>
    </div>
  );
};

export default LeadsTable;
