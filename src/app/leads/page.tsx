import { Suspense } from 'react';
import { UserProfile } from '@/types/profile';
import { getUserProfile } from '@/app/actions/profile';
import { redirect } from 'next/navigation';
import { getLeads } from '@/app/actions/leads';
import LeadsTable from '@/app/components/leads/LeadsTable';
import { Lead } from '@/types/leads';
import { Pagination } from '@/app/components/Pagination';
import LeadFilters from '@/clients/views/leads/LeadFilters';

const BASE_URL = process.env.NEXT_PUBLIC_BASE_URL || '';

type PageProps = {
  searchParams: {
    offset: string;
    limit: string;
    ordering: string;
    status: string;
  };
};

export default async function LeadsDashboard({ searchParams }: PageProps) {
  const data = await getUserProfile<UserProfile>();

  if (!data.user_id) {
    redirect(BASE_URL);
  }

  const leads = await getLeads<{
    results: Lead[];
    count: number;
  }>(
    Number(searchParams?.offset ?? 0),
    Number(searchParams?.limit ?? 10),
    searchParams?.ordering ?? '-created_at',
    searchParams?.status ?? 'discussion%2Cnot_started'
  );

  return (
    <main className="min-h-screen py-4 px-5 md:py-5 md:px-10 bg-slate-50">
      <div className="flex items-center justify-between mb-3">
        <p className="text-2xl font-semibold leading-[32px] tracking-[-0.6px]">
          Rental Leads
        </p>
        {/* <Button className="bg-[#0F172A] hover:bg-[#0F172A]/70 rounded-md !px-3 !py-2 text-sm font-medium">
          + New Lead
        </Button> */}
      </div>
      <LeadFilters />
      <Suspense fallback={<div className="p-8 py-[60px]">Loading...</div>}>
        {leads?.results && <LeadsTable leads={leads.results} />}
        {leads?.count && leads?.count > 0 && (
          <Pagination
            total={leads?.count}
            pageSize={10}
            queryParams={{
              offset: Number(searchParams?.offset ?? 0),
              limit: Number(searchParams?.limit ?? 0),
              status: searchParams?.status ?? 'discussion%2Cnot_started',
              ordering: searchParams?.ordering ?? '-created_at',
            }}
            prefix="/leads"
          />
        )}
      </Suspense>
    </main>
  );
}
