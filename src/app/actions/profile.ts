'use server';

import { cookies } from 'next/headers';

const BASE_URL = process.env.NEXT_PUBLIC_API_BASE_URL || '';

export const getUserProfile = async <T>() => {
  const cookieStore = cookies();
  const token = cookieStore.get('token')?.value;

  // If no BASE_URL is configured, return a mock profile for development
  if (!BASE_URL) {
    console.log("No API base URL configured, returning mock profile");
    return {
      id: 1,
      name: "Test User",
      email: "<EMAIL>",
      role: "admin"
    } as T;
  }

  try {
    const res = await fetch(`${BASE_URL}/profile`, {
      headers: {
        Authorization: `JWT ${token}`,
      },
      next: { revalidate: 10, tags: ['user-profile'] },
    });

    return res.json() as T;
  } catch (error) {
    console.log('error', error);
    throw new Error('Failed to fetch user profile');
  }
};

export const searchContacts = async <T>(
  query: string,
  type: ('tenant' | 'buyer' | 'seller')[]
) => {
  const cookieStore = cookies();
  const token = cookieStore.get('token')?.value;

  // If no BASE_URL is configured, return empty results for development
  if (!BASE_URL) {
    console.log("No API base URL configured, returning empty contacts");
    return [] as T;
  }

  try {
    const res = await fetch(
      `${BASE_URL}/contacts?${type
        .map((_t) => `type=${_t}`)
        .join('&')}&limit=10&offset=0&search_name_or_email=${query}`,
      {
        headers: {
          Authorization: `JWT ${token}`,
        },
        next: { revalidate: 10, tags: [`search-contacts-${query}`] },
      }
    );

    if (!res.ok) {
      console.log('res', res);
      throw new Error('Failed to fetch contacts');
    }
    return res.json() as T;
  } catch (error) {
    console.log('error', error);
    throw new Error('Failed to fetch contacts');
  }
};

export const searchUsers = async <T>(query: string) => {
  const cookieStore = cookies();
  const token = cookieStore.get('token')?.value;

  try {
    const res = await fetch(
      `${BASE_URL}/users?limit=10&offset=0&first_name=${query}&user_type=pm`,
      {
        headers: {
          Authorization: `JWT ${token}`,
        },
        next: { revalidate: 10, tags: [`search-users-${query}`] },
      }
    );

    if (!res.ok) {
      console.log('res', res);
      throw new Error('Failed to fetch contacts');
    }
    return res.json() as T;
  } catch (error) {
    console.log('error', error);
    throw new Error('Failed to fetch contacts');
  }
};
