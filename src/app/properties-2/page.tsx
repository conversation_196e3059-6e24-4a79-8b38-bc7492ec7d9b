'use server';
import { Listing } from '@/types/property';
import { getAllListings } from '@/app/actions/property';
import { getSearchParams } from '@/utils/common';
import Image from 'next/image';
import PropertiesFilter from '@/clients/views/properties/PropertiesFilter';
import StickyFooter from '@/clients/views/properties/StickyFooter';
import { ListingsContextContainer } from '@/clients/contexts/ListingsContext';
import { Nullable } from '@/types/common';
import { PropertiesTableClient } from '@/clients/views/properties/PropertiesTableClient';

const defaultParams = {
  show_rates: 'true',
  show_price: 'true',
  ordering: 'not_renting_year,priority,-calendar_updated_at',
};

export default async function Properties({
  searchParams,
}: {
  searchParams: { [key: string]: string };
}) {
  const params = getSearchParams({
    ...searchParams,
    ...defaultParams,
  });

  const data = await getAllListings<{
    results: Listing[];
    count: number;
    next?: Nullable<string>;
  }>(params);

  return (
    <ListingsContextContainer
      listings={data?.results ?? []}
      nextLink={data?.next}
      count={data?.count ?? 0}
    >
      <main className="min-h-screen py-4 px-5 md:py-5 md:px-10">
        <div className="flex items-center space-x-3 mb-8">
          <Image
            alt="listings icon"
            src="/images/icons/rental-list.svg"
            width={30}
            height={30}
          />
          <p className="text-xl font-medium text-gray-900">Rental Listings</p>
        </div>
        <PropertiesFilter />

        <PropertiesTableClient />
        <StickyFooter />
      </main>
    </ListingsContextContainer>
  );
}
