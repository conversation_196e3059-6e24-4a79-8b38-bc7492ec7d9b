'use client';

import { getAllListings } from '@/app/actions/property';
import { Nullable } from '@/types/common';
import { Listing } from '@/types/property';

import {
  ReactNode,
  createContext,
  useCallback,
  useContext,
  useEffect,
  useState,
} from 'react';

type Context = {
  selectedIds: number[];
  setSelectedIds: (ids: number[]) => void;
  fetchNext: () => void;
  data: Listing[];
  total: number;
  isUpdatingSearch: boolean;
  setIsUpdatingSearch: (val: boolean) => void;
  onSubset: () => void;
};

export const ListingsContext = createContext<Context>({} as Context);

type ContextProps = {
  children: ReactNode;
  listings: Listing[];
  nextLink?: Nullable<string>;
  count: number;
};

export const ListingsContextContainer = ({
  children,
  listings,
  nextLink,
  count,
}: ContextProps) => {
  // UI state
  const [isUpdatingSearch, setIsUpdatingSearch] = useState(false);
  const [selectedIds, setSelectedIds] = useState<number[]>([]);
  const [data, setData] = useState<Listing[]>(listings);
  const [total, setTotal] = useState<number>(count);
  const [nextPage, setNextPage] = useState<Nullable<string>>(nextLink ?? null);

  // Sort data so that selected items appear first
  const onSubset = useCallback(() => {
    if (selectedIds.length === 0) {
      return;
    }

    const selectedItems = data.filter((item) =>
      selectedIds.includes(item.listing_id)
    );
    const unselectedItems = data.filter(
      (item) => !selectedIds.includes(item.listing_id)
    );

    return setData([...selectedItems, ...unselectedItems]);
  }, [data, selectedIds]);

  const fetchNext = useCallback(() => {
    if (nextPage) {
      getAllListings<{
        results: Listing[];
        count: number;
        next?: Nullable<string>;
      }>(nextPage.split('/listings?')[1]).then((res) => {
        setData((prev) => [...prev, ...(res?.results ?? [])]);
        setTotal(res?.count ?? 0);
        setNextPage(res?.next ?? null);
        setIsUpdatingSearch(false);
      });
    }
  }, [nextPage]);

  useEffect(() => {
    setData(listings);
  }, [listings]);

  return (
    <ListingsContext.Provider
      value={{
        selectedIds,
        setSelectedIds,
        fetchNext,
        data,
        onSubset,
        total,
        isUpdatingSearch,
        setIsUpdatingSearch,
      }}
    >
      {children}
    </ListingsContext.Provider>
  );
};

export const useListings = () => {
  const context = useContext(ListingsContext);
  if (!context) {
    throw new Error(
      'useListings must be used within a ListingsContextProvider'
    );
  }
  return context;
};
