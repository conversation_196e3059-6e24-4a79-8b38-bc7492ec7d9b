'use client';

import { getAllListings } from '@/app/actions/property';
import { Nullable } from '@/types/common';
import { Listing } from '@/types/property';

import {
  ReactNode,
  createContext,
  useCallback,
  useContext,
  useState,
} from 'react';

type Context = {
  selectedIds: number[];
  setSelectedIds: (ids: number[]) => void;
  fetchNext: () => void;
  data: Listing[];
  total: number;
};

export const ListingsContext = createContext<Context>({} as Context);

type ContextProps = {
  children: ReactNode;
  listings: Listing[];
  nextLink?: Nullable<string>;
  count: number;
};

export const ListingsContextContainer = ({
  children,
  listings,
  nextLink,
  count,
}: ContextProps) => {
  const [selectedIds, setSelectedIds] = useState<number[]>([]);
  const [data, setData] = useState<Listing[]>(listings);
  const [total, setTotal] = useState<number>(count);
  const [next, setNext] = useState<Nullable<string>>(nextLink ?? null);

  const fetchNext = useCallback(() => {
    if (next) {
      getAllListings<{
        results: Listing[];
        count: number;
        next?: Nullable<string>;
      }>(next).then((res) => {
        setData((prev) => [...prev, ...(res?.results ?? [])]);
        setTotal(res?.count ?? 0);
        setNext(res?.next ?? null);
      });
    }
  }, [next]);

  return (
    <ListingsContext.Provider
      value={{ selectedIds, setSelectedIds, fetchNext, data, total }}
    >
      {children}
    </ListingsContext.Provider>
  );
};

export const useListings = () => {
  const context = useContext(ListingsContext);
  if (!context) {
    throw new Error(
      'useListings must be used within a ListingsContextProvider'
    );
  }
  return context;
};
