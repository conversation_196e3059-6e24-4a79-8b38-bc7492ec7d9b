import React, { useCallback, useMemo } from 'react';
import { FieldArray, FormikErrors } from 'formik';
import classNames from 'classnames';
import Select from '@/clients/ui/select';
import PaymentScheduleItem from './PaymentScheduleItem';
import dayjs from 'dayjs';
import { FinancialFormValues } from './FinancialForm';
import {
  calculateGrandTotalForPaymentItem,
  generateNewPaymentScheduleForLease,
} from '@/utils/lease';
import { Lease } from '@/types/lease';
import { currencyFormatter } from '@/utils/common';

export interface Payment {
  due_date: string; // ISO string for the date
  rent: number;
  processing_fee?: number;
  other_fee?: number;
  security_deposit?: number;
  occupancy_tax?: number;
}

export interface PaymentScheduleValues {
  payments: Payment[];
}

interface PaymentScheduleProps {
  values: PaymentScheduleValues;
  errors?: FormikErrors<PaymentScheduleValues>;
  setFieldValue: (field: string, value: any, validate?: boolean) => void;
  validateField: (field: string) => void;
  rent: number;
  financialInfo: FinancialFormValues;
  dates?: {
    from: Date;
    to: Date;
  };
  lease?: Lease;
}

const PaymentSchedule: React.FC<PaymentScheduleProps> = ({
  values,
  errors,
  setFieldValue,
  validateField,
  dates,
  // rent,
  financialInfo,
  lease,
}) => {
  const onChangeSelect = useCallback(
    (value: { id: string | number; name: string }, name?: string) => {
      const totalPayments = Number(value.id);

      if (!dates) {
        return;
      }

      const newPayments = generateNewPaymentScheduleForLease(
        dates,
        Number(financialInfo.rent ?? 0),
        financialInfo.processingFee,
        financialInfo.securityDeposit,
        Number(financialInfo.occupancyTax.amount ?? 0),
        financialInfo.otherFees as any,
        financialInfo.occupancyTax?.exempt,
        totalPayments
      );

      setFieldValue('paymentSchedule.payments', newPayments);
    },
    [dates, financialInfo, setFieldValue]
  );

  const isDayOfArrivalMoreThan45Days = useMemo(() => {
    if (!dates) {
      return false;
    }

    return dayjs(dates.from).diff(dayjs(), 'day') >= 45;
  }, [dates]);

  return (
    <div className="flex flex-col gap-y-4">
      <div className="flex justify-between items-center md:justify-start md:gap-x-4">
        <label htmlFor="numberOfPayments" className="text-xs md:text-base">
          Number of Payments
        </label>
        <Select
          className="w-[180px] border p-2 rounded-md"
          bodyClassName="w-[180px] overflow-y-scroll"
          name="numberOfPayments"
          placeholder=""
          options={
            isDayOfArrivalMoreThan45Days
              ? [
                  { id: 1, name: '1' },
                  {
                    id: 2,
                    name: '2',
                  },
                  {
                    id: 3,
                    name: '3',
                  },
                  {
                    id: 4,
                    name: '4',
                  },
                ]
              : [
                  {
                    id: 1,
                    name: '1',
                  },
                ]
          }
          value={values?.payments.length}
          onChange={onChangeSelect}
          disabled={(lease && lease.status !== 'Draft') || !financialInfo.rent}
        />
      </div>

      <FieldArray name="paymentSchedule.payments">
        {() => (
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            {values.payments.map((payment: Payment, index: number) => {
              return (
                <div
                  key={index}
                  className={classNames(
                    'border p-4 rounded-md flex flex-col justify-between',
                    {
                      grow: values.payments.length >= 2,
                    }
                  )}
                >
                  <PaymentScheduleItem
                    values={values}
                    payment={payment}
                    index={index}
                    errors={errors}
                    rent={financialInfo.rent}
                    setFieldValue={setFieldValue}
                    validateField={validateField}
                    disabled={lease && lease.status !== 'Draft'}
                    disableInputs={values.payments.length === 1}
                    isFirstPayment={index === 0}
                    isLastPayment={index === values.payments.length - 1}
                  />
                  <div>
                    <hr className="my-4" />
                    <div className="flex justify-between mt-4 text-xs md:text-base">
                      <h3 className="font-bold">Grand Total</h3>
                      <p className="w-32 text-black text-right pr-2 font-medium">
                        {currencyFormatter.format(
                          calculateGrandTotalForPaymentItem(payment)
                        )}
                      </p>
                    </div>
                  </div>
                </div>
              );
            })}
          </div>
        )}
      </FieldArray>
    </div>
  );
};

export default PaymentSchedule;
