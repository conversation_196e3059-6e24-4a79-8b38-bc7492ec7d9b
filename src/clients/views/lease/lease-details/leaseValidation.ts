import * as Yup from 'yup';

export const leaseInfoSchema = Yup.object({
  tenantName: Yup.string().required('Tenant name is required'),
  listing_address: Yup.string().required('Listing address is required'),
  dates: Yup.object({
    from: Yup.date().required('Start date is required'),
    to: Yup.date().required('End date is required'),
  }),
  is_co_broke_lease: Yup.boolean().optional(),
  represent: Yup.string().optional(),
  co_broke_agency: Yup.string().optional(),
});

export const financialInfoSchema = Yup.object({
  commission: Yup.number().required('Commission is required'),
  rent: Yup.number()
    .min(1, 'Must be greater than 0')
    .required('Rent is required'),
  processingFee: Yup.number().optional(),
  otherFees: Yup.array().of(
    Yup.object({
      reason: Yup.string().required('Fee name is required'),
      taxable: Yup.boolean(),
      amount: Yup.number().required('Amount is required'),
    })
  ),
  occupancyTax: Yup.object({
    amount: Yup.number().required('Occupancy Tax amount is required'),
    exempt: Yup.boolean(),
  }),
  securityDeposit: Yup.number().optional(),
  returning_tenant_info: Yup.string().optional(),
});

export const clausesSchema = Yup.object({
  noSmoking: Yup.boolean(),
  petsOk: Yup.boolean(),
  poolWaiverRequired: Yup.boolean(),
  showClause: Yup.boolean(),
  roofWalkWaiverRequired: Yup.boolean(),
  comment: Yup.string(),
});

export const paymentScheduleSchema = Yup.object({
  payments: Yup.array().of(
    Yup.object({
      due_date: Yup.string().required('Due date is required'),
      rent: Yup.number().required('Rent is required'),
    })
  ),
});

export const leaseValidationSchema = Yup.object({
  leaseInfo: leaseInfoSchema,
  financialInfo: financialInfoSchema,
  clauses: clausesSchema,
  paymentSchedule: paymentScheduleSchema,
});
