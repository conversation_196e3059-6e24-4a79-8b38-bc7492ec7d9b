'use client';

import { Lease } from '@/types/lease';
import AddressAutocomplete from '../../common/AddressAutocomplete';
import { memo, useCallback, useMemo } from 'react';
import SelectTenantInput from './SelectTenantInput';
import { Contact, UserProfile } from '@/types/profile';
import { ListingAddressPayload, Property, Rent } from '@/types/property';
import DateRangePicker from '@/clients/components/common/DateRangePicker';
import { DateRange } from 'react-day-picker';
import DateRangePickerMobile from '@/clients/components/common/DateRangePicker/DateRangePickerMobile';
import { format, isValid } from 'date-fns';
import { ArrowRightIcon } from '@heroicons/react/24/outline';
import FormHelperText from '@/app/ui/form-helper-text';
import { Nullable } from '@/types/common';
import { FormikErrors, FormikTouched } from 'formik';
import {
  ensureNumberHas2Decimals,
  getFullName,
  parseDateString,
} from '@/utils/common';
import dayjs from 'dayjs';
import classNames from 'classnames';
import { useLease } from '@/clients/contexts/LeaseContext';
import { calculateRentForListing } from '@/app/actions/property';
import toast from 'react-hot-toast';
import {
  calculateOccupancyTax,
  generateNewPaymentScheduleForLease,
} from '@/utils/lease';
import { FinancialFormValues } from './FinancialForm';
import Checkbox from '@/clients/ui/checkbox';
import Select from '@/clients/ui/select';

export type LeaseInfoFormValues = {
  listing_address: string;
  tenantName?: string;
  leasingAgent?: string;
  landlordName: string;
  co_broke_agency?: string;
  is_co_broke_lease?: boolean;
  represent?: string;
  dates?: {
    from: Date;
    to: Date;
  };
};

type Props = {
  listingId: Nullable<number>;
  values: LeaseInfoFormValues;
  financialInfo: FinancialFormValues;
  errors?: FormikErrors<LeaseInfoFormValues>;
  touched?: FormikTouched<LeaseInfoFormValues>;
  setFieldValue: (field: string, value: any) => void;
  validateField: (field: string) => void;
  lease?: Lease;
  listingDetails?: Nullable<Property>;
  userData: UserProfile;
  isLegacyBookingRule: boolean;
  validateForm: () => any;
  charge_community_impact_fee?: boolean;
  min_security_deposit?: Nullable<number>;
};

const CO_BROKE_AGENCIES = [
  {
    id: 19,
    name: 'Airbnb',
  },
  {
    id: 1,
    name: 'Atlantic East',
  },
  {
    id: 2,
    name: 'Bamber Real Estate',
  },
  {
    id: 3,
    name: 'Bass Point Realty',
  },
  {
    id: 4,
    name: 'Boyce Realty',
  },
  {
    id: 5,
    name: 'Compass Rose',
  },
  {
    id: 6,
    name: 'Congdon & Coleman',
  },
  {
    id: 7,
    name: 'Fisher Real Estate',
  },
  {
    id: 10,
    name: 'Great Point Properties',
  },
  {
    id: 9,
    name: 'Grey Lady Properties',
  },
  {
    id: 20,
    name: 'Homeaway',
  },
  {
    id: 11,
    name: 'Island Properties',
  },
  {
    id: 13,
    name: 'Jordan Real Estate',
  },
  {
    id: 8,
    name: 'J Pepper Frazier Company',
  },
  {
    id: 12,
    name: 'Lee Real Estate',
  },
  {
    id: 14,
    name: 'Maury People',
  },
  {
    id: 15,
    name: 'Nantucket Realty Advisors',
  },
  {
    id: 16,
    name: 'Shepherd Real Estate',
  },
  {
    id: 21,
    name: 'VRBO',
  },
  {
    id: 22,
    name: 'WeNeedaVacation',
  },
  {
    id: 17,
    name: 'Westbrook Real Estate',
  },
  {
    id: 18,
    name: 'William Raveis',
  },
];

const LeaseInfoForm = ({
  values,
  financialInfo,
  errors,
  setFieldValue,
  validateField,
  lease,
  listingDetails,
  userData,
  validateForm,
  isLegacyBookingRule,
  charge_community_impact_fee,
  min_security_deposit,
}: Props) => {
  const { setRentInfo, listingId, setListingId, setTenant } = useLease();

  const calculateRent = useCallback(
    async (arrival_date: Date, departure_date: Date) => {
      if (listingId) {
        try {
          const rent = await calculateRentForListing<Rent>({
            listing: listingId,
            arrival_date: format(arrival_date, 'yyyy-MM-dd'),
            departure_date: format(departure_date, 'yyyy-MM-dd'),
          });
          if (rent) {
            setRentInfo(rent);
            const rentAmount = Number(rent?.rent ?? 0);
            const processing_fee = isLegacyBookingRule ? 100 : rentAmount * 0.1;
            const security_deposit = isLegacyBookingRule
              ? (min_security_deposit ?? 0.1) * rentAmount
              : 0;
            const occupancy_tax = calculateOccupancyTax(
              rentAmount,
              processing_fee,
              financialInfo.otherFees as any,
              charge_community_impact_fee
            );
            const newPayments = generateNewPaymentScheduleForLease(
              { from: arrival_date, to: departure_date },
              rentAmount,
              processing_fee,
              security_deposit,
              occupancy_tax,
              financialInfo.otherFees as any,
              financialInfo.occupancyTax.exempt
            );

            setFieldValue(
              'financialInfo.processingFee',
              ensureNumberHas2Decimals(processing_fee)
            );
            setFieldValue(
              'financialInfo.securityDeposit',
              ensureNumberHas2Decimals(security_deposit)
            );
            setFieldValue(
              'financialInfo.rent',
              ensureNumberHas2Decimals(rentAmount)
            );
            setFieldValue(
              'financialInfo.occupancyTax.amount',
              ensureNumberHas2Decimals(occupancy_tax)
            );
            setFieldValue('paymentSchedule.payments', newPayments);
            setTimeout(() => {
              validateForm().then((res: any) => console.log({ res }));
            }, 50);
          }
        } catch (error) {
          toast('No rates available for selected dates', {
            style: {
              color: 'orange',
            },
          });

          const newPayments = generateNewPaymentScheduleForLease(
            { from: arrival_date, to: departure_date },
            financialInfo.rent,
            financialInfo.processingFee,
            financialInfo.securityDeposit,
            financialInfo.occupancyTax.amount,
            financialInfo.otherFees as any,
            financialInfo.occupancyTax.exempt
          );

          setFieldValue('paymentSchedule.payments', newPayments);
          setTimeout(() => {
            validateForm().then((res: any) => console.log({ res }));
          }, 50);
        }
      }
    },
    [
      listingId,
      setRentInfo,
      isLegacyBookingRule,
      min_security_deposit,
      financialInfo.otherFees,
      financialInfo.occupancyTax.exempt,
      financialInfo.occupancyTax.amount,
      financialInfo.rent,
      financialInfo.processingFee,
      financialInfo.securityDeposit,
      charge_community_impact_fee,
      setFieldValue,
      validateForm,
    ]
  );

  const editAllowed = useMemo(
    () => (lease && lease?.status === 'Draft') || !lease,
    [lease]
  );

  const onToggleCoBrokeLease = useCallback(
    (e: React.ChangeEvent<HTMLInputElement>) => {
      const { checked } = e.target;
      setFieldValue('leaseInfo.is_co_broke_lease', checked);
    },
    [setFieldValue]
  );

  const onToggleRepresent = useCallback(
    (e: React.ChangeEvent<HTMLInputElement>) => {
      const { id } = e.target;
      setFieldValue(
        'leaseInfo.represent',
        id === 'represent-owner' ? 'owner' : 'tenant'
      );
    },
    [setFieldValue]
  );

  const onSelectAddress = useCallback(
    (_a: ListingAddressPayload) => {
      console.log('address is', _a);
      setListingId(_a.listing_id);
      setFieldValue('leaseInfo.listing_address', _a.address);
      setTimeout(() => validateField('leaseInfo.listing_address'), 10);
      setFieldValue(
        'leaseInfo.landlordName',
        getFullName(_a?.owner_first_name ?? '', _a?.owner_last_name ?? '')
      );
    },
    [setFieldValue, setListingId, validateField]
  );

  const onSelectTenant = useCallback(
    (_t?: Contact) => {
      setFieldValue(
        'leaseInfo.tenantName',
        getFullName(_t?.first_name, _t?.last_name)
      );
      setTimeout(() => validateField('leaseInfo.tenantName'), 10);
      setTenant(_t ?? null);
    },
    [setFieldValue, setTenant, validateField]
  );

  const onDateChange = useCallback(
    (_d?: DateRange) => {
      const { from, to } = _d || {};
      const areDatesValid = from && to && isValid(from) && isValid(to);

      if (areDatesValid) {
        calculateRent(from, to);
      }

      setFieldValue('leaseInfo.dates', _d);
      setTimeout(() => validateField('leaseInfo.dates'), 10);
    },
    [calculateRent, setFieldValue, validateField]
  );

  return (
    <>
      <div className="p-4 bg-navy rounded-[10px] grid md:grid-cols-2 xl:grid-cols-3 gap-2">
        <div className="col-span-3 bg-white rounded-md px-4 py-2 grid md:grid-cols-2 xl:grid-cols-3 gap-2">
          <div>
            <div className="bg-white flex items-center gap-x-2">
              <Checkbox
                checked={values.is_co_broke_lease}
                disabled={!editAllowed || !!lease?.lease_id}
                onChange={onToggleCoBrokeLease}
              />
              <label className="text-[10px] max-w-[70%] leading-normal">
                Co - Broke Lease
              </label>
            </div>
            {values.is_co_broke_lease && (
              <>
                <div className="bg-white flex items-center gap-x-2 my-2">
                  <Checkbox
                    id="represent-owner"
                    checked={values.represent === 'owner'}
                    disabled={!editAllowed || !!lease?.lease_id}
                    onChange={onToggleRepresent}
                  />
                  <label className="text-[10px] max-w-[70%] leading-normal">
                    Representing Owner
                  </label>
                </div>
                <div className="bg-white flex items-center gap-x-2 my-2">
                  <Checkbox
                    id="represent-tenant"
                    checked={values.represent === 'tenant'}
                    disabled={!editAllowed || !!lease?.lease_id}
                    onChange={onToggleRepresent}
                  />
                  <label className="text-[10px] max-w-[70%] leading-normal">
                    Representing Tenant
                  </label>
                </div>
              </>
            )}
          </div>
          {values.is_co_broke_lease && (
            <div className="col-span-2 flex gap-x-2">
              Co-Broke Agency
              <Select className="w-[320px]" options={[]} />
            </div>
          )}
        </div>
        <div className="bg-white rounded-md px-4 py-2 flex items-center gap-x-2 h-10">
          <span className="md:w-[120px] xl:w-[90px] text-xs">Address:</span>
          <div className="w-full">
            <AddressAutocomplete
              name="leaseInfo.listing_address"
              value={values.listing_address}
              placeholder="Listing Address"
              className="!text-xs !p-0 font-semibold"
              onSelectAddress={onSelectAddress}
              disabled={!editAllowed}
            />
            {errors?.listing_address && (
              <FormHelperText error className="text-[10px]">
                {errors?.listing_address}
              </FormHelperText>
            )}
          </div>
        </div>
        <div className="bg-white rounded-md px-4 py-2 flex items-center gap-x-2 h-10">
          <span className="md:w-[120px] xl:w-[90px] text-xs">Tenant:</span>
          <div className="w-full">
            <SelectTenantInput
              name="leaseInfo.tenantName"
              value={values.tenantName}
              onSelectTenant={onSelectTenant}
              disabled={!editAllowed}
            />
            {errors?.tenantName && (
              <FormHelperText error className="text-[10px]">
                Tenant is required
              </FormHelperText>
            )}
          </div>
        </div>
        <div className="bg-white rounded-md px-4 py-2 flex items-center gap-x-2 h-10">
          <span className="md:w-[120px] xl:w-[90px] text-xs">
            Leasing Agent:
          </span>
          <div className="w-full font-medium text-xs">
            {values.leasingAgent}
          </div>
        </div>
        <div className="bg-white rounded-md px-4 py-2 flex items-center gap-x-2 h-10">
          <span className="md:w-[120px] xl:w-[90px] text-xs">Landlord:</span>
          <p className="text-xs font-semibold">{values.landlordName}</p>
        </div>
        <div className="bg-white rounded-md px-4 py-2 flex items-center gap-x-2 h-10">
          <span className="md:w-[120px] xl:w-[90px] text-xs">Dates:</span>
          <div className="hidden md:block w-full">
            <DateRangePicker
              date={values.dates}
              setDate={onDateChange}
              className="w-full p-0"
              availableCalendar={listingDetails?.availabilities ?? []}
              title={
                <div
                  className={classNames(
                    'flex items-center justify-between w-full text-xs font-semibold'
                  )}
                >
                  <p>
                    {values?.dates?.from
                      ? format(values.dates.from, 'LLL d, yyyy')
                      : 'Start date'}
                  </p>
                  <ArrowRightIcon className="w-4 h-4" />
                  <p>
                    {values?.dates?.to
                      ? format(values.dates.to, 'LLL d, yyyy')
                      : 'End date'}
                  </p>
                </div>
              }
              disabled={!editAllowed}
            />
            {errors?.dates && (
              <FormHelperText error className="text-[10px]">
                Dates are required
              </FormHelperText>
            )}
          </div>
          <div className="md:hidden w-full">
            <DateRangePickerMobile
              date={values.dates}
              setDate={onDateChange}
              className="w-full p-0"
              title={
                <div className="flex items-center justify-between w-full text-xs font-semibold">
                  <p>
                    {values?.dates?.from
                      ? format(values.dates.from, 'LLL d, yyyy')
                      : 'Start date'}
                  </p>
                  <ArrowRightIcon className="w-4 h-4" />
                  <p>
                    {values?.dates?.to
                      ? format(values.dates.to, 'LLL d, yyyy')
                      : 'End date'}
                  </p>
                </div>
              }
              disabled={!editAllowed}
              availableCalendar={listingDetails?.availabilities ?? []}
            />
            {errors?.dates && (
              <FormHelperText error className="text-[10px]">
                Dates are required
              </FormHelperText>
            )}
          </div>
        </div>
        <div className="bg-white rounded-md px-4 py-2 flex items-center gap-x-2 h-10">
          <span className="md:w-[120px] xl:w-[90px] text-xs">
            Lease Created:
          </span>
          <p className="text-xs font-semibold">
            {lease ? format(lease.created_at, 'MM/dd/yyyy') : 'N/A'}
          </p>
        </div>
      </div>
    </>
  );
};

export default memo(LeaseInfoForm);
