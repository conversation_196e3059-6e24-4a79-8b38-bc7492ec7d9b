'use client';

import Button from '@/clients/ui/button';
import { useCallback, useState } from 'react';
import { getAuthorizedHeader } from '@/app/actions/lease';
import classNames from 'classnames';
import { downloadFile } from '@/utils/common';

type HeaderProps = {
  leaseId: number;
};

type HeaderState = {
  message: {
    type: 'success' | 'error' | null;
    content: string;
  };
  downloading: boolean;
};

const BASE_URL = process.env.NEXT_PUBLIC_API_BASE_URL || '';

const INITIAL_STATE = {
  message: {
    type: null,
    content: '',
  },
  downloading: false,
};

const Header = ({ leaseId }: HeaderProps) => {
  const [state, setState] = useState<HeaderState>(INITIAL_STATE);

  const downloadContract = useCallback(async () => {
    setState((oldState) => ({
      ...oldState,
      downloading: true,
    }));

    try {
      const headers = getAuthorizedHeader() as HeadersInit;
      const res = await fetch(`${BASE_URL}/get-sign-file?lease=${leaseId}`, {
        headers,
      });

      if (!res.ok) {
        throw new Error('Error downloading file');
      }

      const data = await res.blob();

      const fileName =
        res.headers.get('content-disposition')?.split('=#')[1] ?? 'file';

      downloadFile(data, `contract-${fileName}`, 'application/pdf');

      setState((oldState) => ({
        ...oldState,
        message: {
          type: 'success',
          content: 'Download sign file successful',
        },
      }));
    } catch (error) {
      setState((oldState) => ({
        ...oldState,
        message: {
          type: 'error',
          content: 'Download sign file fail, please try again later',
        },
      }));
    } finally {
      setState((oldState) => ({
        ...oldState,
        downloading: false,
      }));

      setTimeout(() => {
        setState(INITIAL_STATE);
      }, 1500);
    }
  }, [leaseId]);

  return (
    <div className="flex justify-between items-center pb-4">
      <div className="overflow-auto text-md font-bold text-black">
        Lease agreement
      </div>
      <Button
        disabled={state.downloading}
        className={classNames('bg-navy text-sm font-normal hover:bg-navy', {
          'opacity-50': state.downloading,
        })}
        onClick={downloadContract}
      >
        {state.downloading ? 'Downloading PDF...' : 'Download PDF'}
      </Button>
    </div>
  );
};

export default Header;
