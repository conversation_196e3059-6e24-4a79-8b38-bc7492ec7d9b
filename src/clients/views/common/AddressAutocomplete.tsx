'use client';

import { useCallback, useMemo, useState } from 'react';

import Autocomplete, { AutocompleteOption } from '@/clients/ui/autocomplete';
import { getListingAddresses } from '@/app/actions/property';
import { ListingAddressPayload } from '@/types/property';
import { twMerge } from 'tailwind-merge';

type Props = {
  className?: string;
  wrapperClassName?: string;
  placeholder?: string;
  value?: string;
  onSelectAddress?: (_a: ListingAddressPayload) => void;
  name?: string;
  disabled?: boolean;
};

const AddressAutocomplete = ({
  className = '',
  wrapperClassName = '',
  placeholder = '',
  value = '',
  onSelectAddress,
  name,
  disabled,
}: Props) => {
  const [address, setAdress] = useState<string>(value);
  const [isFetching, setIsFetching] = useState<boolean>(false);
  const [addresses, setAddresses] = useState<ListingAddressPayload[]>([]);

  const options = useMemo(
    () =>
      addresses?.map((_address) => ({
        id: _address.listing_id,
        label: _address.address,
        value: _address.address,
      })),
    [addresses]
  );

  const onSelect = useCallback(
    (option: AutocompleteOption) => {
      setAdress(option.value);
      const address = addresses?.find((_a) => _a.listing_id === option.id);
      if (address && onSelectAddress) {
        onSelectAddress?.(address);
      }
    },
    [addresses, onSelectAddress]
  );

  const fetchAddresses = useCallback((query = '') => {
    setIsFetching(true);
    getListingAddresses<{ results: ListingAddressPayload[] }>(query)
      .then(({ results }) => {
        setAddresses(results);
        setIsFetching(false);
      })
      .catch((err) => {
        console.error(err);
        setIsFetching(false);
      });
  }, []);

  return (
    <Autocomplete
      name={name}
      value={address}
      className={twMerge(
        'text-xs md:text-sm border-0 px-0 py-0 md:py-2 w-full',
        className
      )}
      wrapperClassName={wrapperClassName}
      placeholder={placeholder}
      options={options}
      onChangeValue={(text: string) => setAdress(text)}
      isFetchingData={isFetching}
      fetchData={fetchAddresses}
      onSelect={onSelect}
      disabled={disabled}
    />
  );
};

export default AddressAutocomplete;
