'use client';

import { memo, ReactNode, useEffect, useState } from 'react';

import { useMediaQuery } from 'react-responsive';

import SlideUpPane from './SlideUpPane';
import Modal from '@/clients/ui/modal';

type ResponsiveDialogProps = {
  open: boolean;
  children: ReactNode;
  hideCloseButton?: boolean;
  dialogClassName?: string;
  drawerClassName?: string;
  drawerContentClassName?: string;
  preventOutsideInteraction?: boolean;
  mobileBreakpoint?: number;
  description?: string;
  onClose?: () => void;
};

/**
 * A responsive dialog component that renders as a modal dialog on desktop
 * and as a bottom drawer on mobile devices.
 */
const ResponsiveDialog = ({
  open,
  children,
  hideCloseButton = false,
  drawerClassName = '',
  drawerContentClassName = '',
  mobileBreakpoint = 768,
  dialogClassName,
  onClose,
}: ResponsiveDialogProps) => {
  const isMobile = useMediaQuery({ maxWidth: mobileBreakpoint });
  const [mounted, setMounted] = useState(false);

  // Handle SSR - ensure we only render the correct component after mounting
  useEffect(() => {
    setMounted(true);
  }, []);

  // Don't render anything during SSR to prevent hydration mismatch
  if (!mounted) return null;

  // For mobile devices, use a drawer
  if (isMobile) {
    return (
      <SlideUpPane isShowing={open} wrapperClassName={drawerClassName}>
        <div
          className={`p-4 flex-1 overflow-auto pb-10 ${drawerContentClassName}`}
        >
          {children}
        </div>
      </SlideUpPane>
    );
  }

  // For desktop, use a dialog
  return (
    <Modal
      open={open}
      showClose={!hideCloseButton}
      className={dialogClassName}
      onClose={onClose}
    >
      {children}
    </Modal>
  );
};

export default memo(ResponsiveDialog);
