'use client';

import LoadingSpinner from '@/clients/ui/loading-spinner';
import dynamic from 'next/dynamic';
import { ReactNode, useCallback, useState } from 'react';
import toast from 'react-hot-toast';

const ShareListingModal = dynamic(() => import('./ShareListingModal'), {
  ssr: false,
  loading: () => (
    <div className="fixed inset-0 bg-white/70 flex items-center justify-center z-[9999] rounded">
      <LoadingSpinner className="w-10 h-10 text-olive" />
    </div>
  ),
});

type Props = {
  propertyIds: number[];
  children?: ReactNode;
  className?: string;
};

const ShareListingButton = ({
  propertyIds = [],
  children,
  className = '',
}: Props) => {
  const [showModal, setShowModal] = useState<boolean>(false);

  const onClick = useCallback(() => {
    if (propertyIds.length === 0) {
      toast.error('Please select at least one listing');
      return;
    }
    setShowModal(true);
  }, [propertyIds.length]);

  const onClose = useCallback(() => {
    setShowModal(false);
  }, []);

  return (
    <>
      <div onClick={onClick} className={className}>
        {children}
      </div>
      {showModal && (
        <ShareListingModal onClose={onClose} propertyIds={propertyIds} />
      )}
    </>
  );
};

export default ShareListingButton;
