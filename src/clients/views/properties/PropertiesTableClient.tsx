'use client';
import React, {
  useState,
  useCallback,
  useEffect,
  useRef,
  useMemo,
  useContext,
} from 'react';
import { useSearchParams } from 'next/navigation';
import { twMerge } from 'tailwind-merge';
import PropertyTableItem from './PropertyTableItem';
import { useUpdateSearchParam } from '@/hooks/useUpdateSearchParam';
import LoadingSpinner from '@/clients/ui/loading-spinner';
import { ListingsContext } from '@/clients/contexts/ListingsContext';
import { DEFAULT_COLUMNS, SortConfig } from './types';

export const PropertiesTableClient = ({}) => {
  const {
    fetchNext,
    data,
    total,
    isUpdatingSearch,
    setIsUpdatingSearch,
    selectedIds,
    setSelectedIds,
  } = useContext(ListingsContext);
  const updateParam = useUpdateSearchParam();
  const searchParams = useSearchParams();
  // State
  const [sortConfig, setSortConfig] = useState<SortConfig>({
    key: null,
    direction: 'asc',
  });

  const listingsContainer = useRef<HTMLDivElement>(null);
  const hasNextPage = useMemo(() => data.length < total, [data.length, total]);

  // Event handlers
  const handleSelectAll = () => {
    if (selectedIds.length === data.length) {
      setSelectedIds([]);
    } else {
      setSelectedIds(data.map((item) => item.listing_id));
    }
  };

  const handleSort = useCallback(
    (key: string) => {
      let direction: 'asc' | 'desc' = 'asc';
      if (sortConfig.key === key && sortConfig.direction === 'asc') {
        direction = 'desc';
      }
      setSortConfig({ key, direction });
      setIsUpdatingSearch(true);
      updateParam(
        'ordering',
        `not_renting_year,${direction === 'asc' ? key : `-${key}`}`
      );
    },
    [setIsUpdatingSearch, sortConfig.direction, sortConfig.key, updateParam]
  );

  const onScrolledToBottom = useCallback(() => {
    const container = listingsContainer?.current;
    if (hasNextPage && container) {
      const { scrollTop, scrollHeight, clientHeight } = container;
      // Check if scrolled to bottom (with small threshold for precision)

      if (scrollTop + clientHeight >= scrollHeight - 5 && !isUpdatingSearch) {
        setIsUpdatingSearch(true);
        fetchNext();
      }
    }
  }, [fetchNext, hasNextPage, isUpdatingSearch, setIsUpdatingSearch]);

  // Reset loading state when search parameters change
  useEffect(() => {
    setIsUpdatingSearch(false);
  }, [searchParams, setIsUpdatingSearch]);

  return (
    <>
      {total > 0 && (
        <div className="mt-[30px] flex gap-x-4 items-center">
          <p className="text-xs font-medium">
            Show 1 - {data.length} of {total} listings
          </p>
        </div>
      )}
      <div className="bg-white shadow-sm rounded-lg overflow-hidden">
        <div
          ref={listingsContainer}
          onScroll={onScrolledToBottom}
          className="overflow-x-auto no-scrollbar mt-[30px] max-h-[96dvh] flex flex-col pb-16"
        >
          <table className="min-w-full divide-y divide-gray-200 table-fixed">
            <thead className="bg-gray-50 sticky top-0 z-10">
              <tr>
                {DEFAULT_COLUMNS.map((column) => (
                  <th
                    key={column.key}
                    className={twMerge(
                      `px-2.5 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider ${column.className}`
                    )}
                  >
                    {column.key === 'checkbox' ? (
                      <input
                        type="checkbox"
                        checked={
                          selectedIds.length === data.length && data.length > 0
                        }
                        onChange={handleSelectAll}
                        className="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
                      />
                    ) : (
                      <button
                        onClick={() =>
                          column.sortable && handleSort(column.key)
                        }
                        className={`flex items-center space-x-1 uppercase font-medium ${
                          column.sortable
                            ? 'hover:text-gray-700 cursor-pointer'
                            : ''
                        }`}
                        disabled={!column.sortable}
                      >
                        <span>{column.label}</span>
                        {column.sortable && sortConfig.key === column.key && (
                          <span className="text-blue-600">
                            {sortConfig.direction === 'asc' ? '↑' : '↓'}
                          </span>
                        )}
                      </button>
                    )}
                  </th>
                ))}
              </tr>
            </thead>
            <tbody className="bg-white divide-y divide-gray-200 overflow-y-auto pb-10">
              {data.map((property) => (
                <PropertyTableItem
                  key={property.listing_id}
                  property={property}
                />
              ))}
            </tbody>
          </table>
        </div>

        {isUpdatingSearch && (
          <div className="fixed inset-0 bg-white/70 flex items-center justify-center z-[9999] rounded">
            <LoadingSpinner className="w-10 h-10 text-olive" />
          </div>
        )}
      </div>
    </>
  );
};
