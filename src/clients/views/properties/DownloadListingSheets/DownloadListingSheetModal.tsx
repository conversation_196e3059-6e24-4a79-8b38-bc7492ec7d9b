import Button from '@/clients/ui/button';
import Checkbox from '@/clients/ui/checkbox';
import Modal from '@/clients/ui/modal';
import { ProgressStatus } from '@/types/common';
import { downloadFile } from '@/utils/common';
import { XMarkIcon } from '@heroicons/react/24/outline';
import { useCallback, useState } from 'react';

enum DOWNLOAD_TYPE {
  LISTING_SHEET_WITH_PHOTOS = 'LISTING_SHEET_WITH_PHOTOS',
  LISTING_SHEET_WITHOUT_PHOTOS = 'LISTING_SHEET_WITHOUT_PHOTOS',
  LISTING_PHOTOS_ONLY = 'LISTING_PHOTOS_ONLY',
}

type Props = {
  onClose: () => void;
  propertyIds: number[];
};

const DownloadListingSheetModal = ({ onClose, propertyIds }: Props) => {
  const [progressStatus, setProgressStatus] = useState<ProgressStatus | null>(
    null
  );
  const [selectedType, setSelectedType] = useState<DOWNLOAD_TYPE>(
    DOWNLOAD_TYPE.LISTING_SHEET_WITH_PHOTOS
  );

  const onSelectType = useCallback((type: DOWNLOAD_TYPE) => {
    setSelectedType(type);
  }, []);

  const onDownload = useCallback(async () => {
    setProgressStatus(ProgressStatus.LOADING);

    try {
      if (selectedType === DOWNLOAD_TYPE.LISTING_SHEET_WITH_PHOTOS) {
        // Download listing sheet first
        const listingSheetResponse = await fetch(
          '/api/download-listing-sheet',
          {
            method: 'POST',
            body: JSON.stringify({ listings: propertyIds }),
          }
        );

        if (!listingSheetResponse.ok) {
          throw new Error('Failed to fetch listing sheet');
        }

        const listingSheetBlob = await listingSheetResponse.blob();
        downloadFile(
          listingSheetBlob,
          `Listings_${propertyIds.join('-')}`,
          'application/pdf'
        );

        // Download all photos and wait for completion
        const photoDownloadPromises = propertyIds.map(async (id) => {
          const response = await fetch('/api/download-listing-photos', {
            method: 'POST',
            body: JSON.stringify({ listing: id }),
          });

          if (!response.ok) {
            throw new Error(`Failed to fetch photos for listing ${id}`);
          }

          const blob = await response.blob();
          downloadFile(blob, `Listing-photos-${id}`, 'application/pdf');
        });

        await Promise.all(photoDownloadPromises);
        setProgressStatus(ProgressStatus.SUCCESSFUL);
      } else if (selectedType === DOWNLOAD_TYPE.LISTING_SHEET_WITHOUT_PHOTOS) {
        const response = await fetch('/api/download-listing-sheet', {
          method: 'POST',
          body: JSON.stringify({ listings: propertyIds }),
        });

        if (!response.ok) {
          throw new Error('Failed to fetch listing sheet');
        }

        const blob = await response.blob();
        downloadFile(
          blob,
          `Listings_${propertyIds.join('-')}`,
          'application/pdf'
        );
        setProgressStatus(ProgressStatus.SUCCESSFUL);
      } else if (selectedType === DOWNLOAD_TYPE.LISTING_PHOTOS_ONLY) {
        // Download all photos and wait for completion
        const photoDownloadPromises = propertyIds.map(async (id) => {
          const response = await fetch('/api/download-listing-photos', {
            method: 'POST',
            body: JSON.stringify({ listing: id }),
          });

          if (!response.ok) {
            throw new Error(`Failed to fetch photos for listing ${id}`);
          }

          const blob = await response.blob();
          downloadFile(blob, `Listing-photos-${id}`, 'application/pdf');
        });

        await Promise.all(photoDownloadPromises);
        setProgressStatus(ProgressStatus.SUCCESSFUL);
      }
    } catch (error) {
      console.log('error', error);
      setProgressStatus(ProgressStatus.FAILED);
    }
  }, [propertyIds, selectedType]);

  return (
    <Modal open className="w-full md:w-[620px] p-6 !rounded-md">
      <>
        <div className="flex items-center justify-between">
          <p className="md:text-[19px] font-semibold">Choose to Download</p>
          <XMarkIcon className="cursor-pointer w-4 h-4" onClick={onClose} />
        </div>
        <hr className="mt-2 mb-4" />
        <div className="flex items-center gap-2 my-2 px-4">
          <Checkbox
            className="w-4 h-4"
            checked={selectedType === DOWNLOAD_TYPE.LISTING_SHEET_WITH_PHOTOS}
            onChange={() =>
              onSelectType(DOWNLOAD_TYPE.LISTING_SHEET_WITH_PHOTOS)
            }
          />
          <p className="flex-grow text-sm">Listing Sheet with Photos</p>
        </div>
        <div className="flex items-center gap-2 my-2 px-4">
          <Checkbox
            className="w-4 h-4"
            checked={
              selectedType === DOWNLOAD_TYPE.LISTING_SHEET_WITHOUT_PHOTOS
            }
            onChange={() =>
              onSelectType(DOWNLOAD_TYPE.LISTING_SHEET_WITHOUT_PHOTOS)
            }
          />
          <p className="flex-grow text-sm">Listing Sheet without Photos</p>
        </div>
        <div className="flex items-center gap-2 my-2 px-4">
          <Checkbox
            className="w-4 h-4"
            checked={selectedType === DOWNLOAD_TYPE.LISTING_PHOTOS_ONLY}
            onChange={() => onSelectType(DOWNLOAD_TYPE.LISTING_PHOTOS_ONLY)}
          />
          <p className="flex-grow text-sm">Listing Photos Only</p>
        </div>

        <div className="w-full flex items-center justify-between mt-4">
          <Button
            intent="secondary"
            className="rounded-lg text-sm font-normal"
            onClick={onClose}
          >
            Cancel
          </Button>
          <Button
            className="rounded-lg text-sm font-normal w-[120px]"
            onClick={onDownload}
            disabled={
              progressStatus === ProgressStatus.LOADING || !selectedType
            }
            isLoading={progressStatus === ProgressStatus.LOADING}
          >
            Download
          </Button>
        </div>
      </>
    </Modal>
  );
};

export default DownloadListingSheetModal;
