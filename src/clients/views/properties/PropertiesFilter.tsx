'use client';

import {
  useCallback,
  useState,
  useEffect,
  ChangeEvent,
  useContext,
} from 'react';
import { useRouter, useSearchParams, usePathname } from 'next/navigation';
import Checkbox from '@/clients/ui/checkbox';
import { ListingsContext } from '@/clients/contexts/ListingsContext';
import PropertiesFilterComponent from './PropertiesFilterComponent';
import FiltersButton from './Filters/FiltersButton';

const BASE_URL = process.env.NEXT_PUBLIC_BASE_URL || '';

export const SELECT_OPTIONS = Array(100)
  .fill(1)
  .map((_, index) => ({
    id: (index + 1).toString(),
    name: (index + 1).toString(),
  }));

const PropertiesFilter = () => {
  const router = useRouter();
  const searchParams = useSearchParams();
  const pathname = usePathname();

  const { setIsUpdatingSearch } = useContext(ListingsContext);
  const [formData, setFormData] = useState({
    show_inactive: false,
    show_without_price: false,
  });

  const onChangeCheckbox = useCallback(
    (e: ChangeEvent<HTMLInputElement>) => {
      const { name, checked } = e.target;
      setIsUpdatingSearch(true);
      setFormData((_s) => ({
        ..._s,
        [name]: checked,
      }));
      // Create new URLSearchParams from current search params
      const params = new URLSearchParams(searchParams.toString());

      // Update or add the min_price parameter
      if (checked) {
        params.set(name, 'true');
      } else {
        params.delete(name);
      }

      // Navigate to the updated URL
      router.push(`${pathname}?${params.toString()}`);
    },
    [pathname, router, searchParams, setIsUpdatingSearch]
  );

  // Reset loading state when search parameters change
  useEffect(() => {
    setIsUpdatingSearch(false);
  }, [searchParams, setIsUpdatingSearch]);

  return (
    <>
      <div className="hidden md:block">
        <PropertiesFilterComponent />
      </div>
      <div className="md:hidden flex items-center gap-x-4">
        <a
          href={`${BASE_URL}/property`}
          className="text-sm text-white font-medium my-2 px-4 py-2 rounded text-center h-10 bg-olive hover:bg-olive/80"
        >
          + New Listing
        </a>

        <FiltersButton />
      </div>

      <div className="hidden md:flex items-center gap-x-2 my-2">
        <Checkbox
          className="w-4 h-4"
          name="show_without_price"
          checked={formData.show_without_price}
          onChange={onChangeCheckbox}
        />
        <label className="text-sm">Include listings without rates</label>
      </div>

      <div className="hidden md:flex items-center gap-x-2 my-2">
        <Checkbox
          className="w-4 h-4"
          name="show_inactive"
          checked={formData.show_inactive}
          onChange={onChangeCheckbox}
        />
        <label className="text-sm">Include inactive listings</label>
      </div>
    </>
  );
};

export default PropertiesFilter;
