'use client';

import { useListings } from '@/clients/contexts/ListingsContext';
import ShareListingButton from '@/clients/views/common/ShareListingButton';
import DownloadListingSheetButton from './DownloadListingSheets/DownloadListingSheetButton';
import { cn } from '@/lib/utils';
import EmailHomeownersButton from './EmailHomeowners/EmailHomeownersButton';
import Button from '@/clients/ui/button';

type Props = {};

const StickyFooter = ({}: Props) => {
  const { selectedIds, onSubset } = useListings();
  return (
    <div
      className={cn(
        'fixed left-0 right-0 bottom-0 p-4 bg-white shadow-card flex flex-col md:flex-row items-center justify-center gap-2 md:gap-4',
        { 'hidden md:flex': selectedIds.length === 0 }
      )}
    >
      <Button
        intent="outline"
        className="bg-white hover:bg-teal-gray border border-solid border-teal-gray text-teal-gray hover:text-white text-sm font-medium cursor-pointer py-2 px-2 text-center rounded w-[200px]"
        onClick={onSubset}
      >
        Subset
      </Button>
      <ShareListingButton
        propertyIds={selectedIds}
        className="bg-white hover:bg-teal-gray border border-solid border-teal-gray text-teal-gray hover:text-white text-sm font-medium cursor-pointer py-2 px-2 text-center rounded w-[200px]"
      >
        Share Listings
      </ShareListingButton>
      <DownloadListingSheetButton
        propertyIds={selectedIds}
        className="bg-white hover:bg-teal-gray border border-solid border-teal-gray text-teal-gray hover:text-white text-sm font-medium cursor-pointer py-2 px-2 text-center rounded w-[200px]"
      >
        Download Listing Sheets
      </DownloadListingSheetButton>
      <EmailHomeownersButton
        propertyIds={selectedIds}
        className="bg-white hover:bg-teal-gray border border-solid border-teal-gray text-teal-gray hover:text-white text-sm font-medium cursor-pointer py-2 px-2 text-center rounded w-[200px]"
      >
        Email Homeowners
      </EmailHomeownersButton>
    </div>
  );
};

export default StickyFooter;
