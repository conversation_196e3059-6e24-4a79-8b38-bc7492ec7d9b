'use client';

import { useListings } from '@/clients/contexts/ListingsContext';
import ShareListingButton from '@/clients/views/common/ShareListingButton';
import DownloadListingSheetButton from './DownloadListingSheets/DownloadListingSheetButton';
import { cn } from '@/lib/utils';
import EmailHomeownersButton from './EmailHomeowners/EmailHomeownersButton';

type Props = {};

const StickyFooter = ({}: Props) => {
  const { selectedIds } = useListings();
  return (
    <div
      className={cn(
        'fixed left-0 right-0 bottom-0 p-4 bg-white shadow-card flex items-center justify-center gap-x-4',
        {}
      )}
    >
      <ShareListingButton
        propertyIds={selectedIds}
        className="bg-white hover:bg-teal-gray border border-solid border-teal-gray text-teal-gray hover:text-white text-sm font-medium cursor-pointer py-2 px-4 rounded"
      >
        Share Listings
      </ShareListingButton>
      <DownloadListingSheetButton
        propertyIds={selectedIds}
        className="bg-white hover:bg-teal-gray border border-solid border-teal-gray text-teal-gray hover:text-white text-sm font-medium cursor-pointer py-2 px-4 rounded"
      >
        Download Listing Sheets
      </DownloadListingSheetButton>
      <EmailHomeownersButton
        propertyIds={selectedIds}
        className="bg-white hover:bg-teal-gray border border-solid border-teal-gray text-teal-gray hover:text-white text-sm font-medium cursor-pointer py-2 px-4 rounded"
      >
        Email Homeowners
      </EmailHomeownersButton>
    </div>
  );
};

export default StickyFooter;
