'use client';

import { useListings } from '@/clients/contexts/ListingsContext';
import { Listing } from '@/types/property';
import classNames from 'classnames';
import dayjs from 'dayjs';
import Image from 'next/image';
import Link from 'next/link';
import { useSearchParams } from 'next/navigation';
import { memo, useCallback } from 'react';

const TurnoverDayMap = {
  Saturday: { subtract: -1, add: 6 },
  Friday: { subtract: -2, add: 5 },
  Thursday: { subtract: -3, add: 4 },
  Wednesday: { subtract: -4, add: 3 },
  Tuesday: { subtract: -5, add: 2 },
  Monday: { subtract: -6, add: 1 },
  Sunday: { subtract: 0, add: 7 },
};

type Props = {
  property: Listing;
};

const PropertyTableItem = ({ property }: Props) => {
  const searchParams = useSearchParams();
  const { selectedIds, setSelectedIds } = useListings();

  const onSelectItem = useCallback(() => {
    const isSelected = selectedIds.find((item) => item === property.listing_id);

    if (isSelected) {
      const newSelected = selectedIds.filter(
        (item) => item !== property.listing_id
      );
      setSelectedIds(newSelected);
    } else {
      const newSelected = [...selectedIds, property.listing_id];
      setSelectedIds(newSelected);
    }
  }, [property.listing_id, selectedIds, setSelectedIds]);

  return (
    <tr
      className={classNames(
        'hover:bg-gray-50 cursor-pointer text-medium-dark-gray',
        {
          'bg-blue-50': selectedIds.find(
            (item) => item === property.listing_id
          ),
          'bg-[rgb(255_0_0)] bg-opacity-10': property.not_renting_year,
        }
      )}
    >
      <td className="p-2.5 whitespace-nowrap">
        <input
          type="checkbox"
          checked={!!selectedIds.find((item) => item === property.listing_id)}
          onChange={onSelectItem}
          className="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
        />
      </td>
      <td className="p-2.5 whitespace-nowrap">
        <Link
          href={`/property/${property.listing_id}/calendar`}
          className="flex items-center justify-start w-[260px]"
        >
          <Image
            alt="Calendar icon"
            width={0}
            height={0}
            className="w-6 h-6 mr-2.5"
            src="/images/icons/ico-calendar.svg"
            quality={65}
          />

          {property?.rates?.length === 0 ? (
            <p>
              {property.year_max_price ? (
                <span>
                  Max Price | ${property.year_max_price.toLocaleString()}
                </span>
              ) : property.max_price ? (
                <span>Max Price | ${property.max_price.toLocaleString()}</span>
              ) : null}
            </p>
          ) : (
            <div className="">
              {property?.rates?.slice(0, 3).map((item, index) => {
                const dayConfig =
                  TurnoverDayMap[
                    property.turnover_day as keyof typeof TurnoverDayMap
                  ] || TurnoverDayMap['Sunday'];

                return (
                  <div key={index}>
                    {searchParams.get('start_date') !==
                      dayjs(item.from_date)
                        .add(dayConfig.add, 'day')
                        .format('YYYY-MM-DD') && (
                      <p className="my-4">
                        {dayjs(item.to_date).format('MM/DD/YYYY')} -{' '}
                        {dayjs(item.from_date)
                          .add(dayConfig.add, 'day')
                          .format('MM/DD/YYYY')}{' '}
                        | <br />
                        <b>${item.weekly_amount.toLocaleString()}</b>
                      </p>
                    )}
                  </div>
                );
              })}
            </div>
          )}
        </Link>
      </td>
      <td className="p-2.5 whitespace-nowrap">
        <Link href={`/property/${property.listing_id}/photo`}>
          <Image
            alt="Listing photo"
            src={
              property.small_url ??
              property.url ??
              '/images/placeholder-house.png'
            }
            width={0}
            height={0}
            sizes="120px"
            className="h-auto object-cover w-[120px] max-w-[120px]"
          />
        </Link>
      </td>
      <td className="break-words whitespace-normal p-2.5">
        <Link href={`/property/${property.listing_id}/location`}>
          <p className="w-[180px]">{property.address}</p>
        </Link>
      </td>
      <td className="break-words whitespace-normal p-2.5">
        <Link href={`/property/${property.listing_id}/location`}>
          <p className="w-[140px]">{property.area_name}</p>
        </Link>
      </td>
      <td className="p-2.5">
        <Link href={`/property/${property.listing_id}/bedroomsandbathrooms`}>
          <p className="w-[60px]">{property.bedroom_number}</p>
        </Link>
      </td>
      <td className="p-2.5">
        <Link href={`/property/${property.listing_id}/bedroomsandbathrooms`}>
          <p className="w-[60px]">{property.bathroom_number}</p>
        </Link>
      </td>
      <td className="p-2.5">
        <Link href={`/property/${property.listing_id}/general`}>
          <p className="w-[60px]">{property.capacity}</p>
        </Link>
      </td>
      <td className="p-2.5 break-words whitespace-normal">
        <Link href={`/property/${property.listing_id}/general`}>
          <p className="w-[180px]">
            {property?.owner_first_name ?? ''} {property?.owner_last_name ?? ''}
          </p>
        </Link>
      </td>
      <td className="p-2.5 break-words whitespace-normal">
        <Link href={`/property/${property.listing_id}/general`}>
          <p className="w-[120px]">{property.turnover_day}</p>
        </Link>
      </td>
      <td className="p-2.5 break-words whitespace-normal">
        <Link href={`/property/${property.listing_id}/calendar`}>
          <p className="w-[120px]">
            {dayjs(property.calendar_updated_at).format('MM-DD-YYYY')}
          </p>
        </Link>
      </td>
      <td className="p-2.5 break-words whitespace-normal">
        <Link href={`/property/${property.listing_id}/general`}>
          <p className="w-[120px]">{property.key_number ?? ''}</p>
        </Link>
      </td>
      <td className="p-2.5 break-words whitespace-normal">
        <Link href={`/property/${property.listing_id}/general`}>
          <p className="w-[200px]">
            {property.latest_leased_info &&
              `${dayjs(property.latest_leased_info.leased_at).format(
                'MM-DD-YYYY'
              )} by ${property.latest_leased_info.leased_by ?? 'N/A'}`}
          </p>
        </Link>
      </td>
    </tr>
  );
};

export default memo(PropertyTableItem);
