'use client';

import { emailHomeowners } from '@/app/actions/property';
import Button from '@/clients/ui/button';
import Modal from '@/clients/ui/modal';
import Textarea from '@/clients/ui/textarea';
import { ProgressStatus } from '@/types/common';
import { XMarkIcon } from '@heroicons/react/24/outline';
import { useCallback, useState } from 'react';
import toast from 'react-hot-toast';

type Props = {
  onClose: () => void;
  propertyIds: number[];
};

const EmailHomeownersModal = ({ onClose, propertyIds }: Props) => {
  const [text, setText] = useState<string>('');
  const [progressStatus, setProgressStatus] = useState<ProgressStatus | null>(
    null
  );

  const onChangeTextInput = useCallback((event: any) => {
    const { name, value } = event.target;

    setText(value);
  }, []);

  const onShare = useCallback(() => {
    if (text.trim().length === 0) {
      toast.error('Please select at least one listing to share');
      return;
    }

    setProgressStatus(ProgressStatus.LOADING);
    emailHomeowners({
      listings: propertyIds,
      email_body: text.trim(),
    })
      .then((res) => {
        setProgressStatus(ProgressStatus.SUCCESSFUL);
        toast.success('Successfully sent Emails to the selected owners!');
        onClose();
      })
      .catch((err) => {
        setProgressStatus(ProgressStatus.FAILED);
      });
  }, [onClose, propertyIds, text]);

  return (
    <Modal open className="w-full md:w-[620px] p-6 !rounded-md">
      <>
        <div className="flex items-center justify-between">
          <p className="md:text-[19px] font-semibold">Email Homeowners</p>
          <XMarkIcon className="cursor-pointer w-4 h-4" onClick={onClose} />
        </div>
        <hr className="mt-2 mb-4" />
        <Textarea
          name="email_body"
          rows={6}
          className="w-full my-2 text-sm"
          placeholder="Message to share..."
          onChange={onChangeTextInput}
          value={text}
        />
        <div className="w-full flex items-center justify-between mt-4">
          <Button
            intent="secondary"
            className="rounded-lg text-sm font-normal"
            onClick={onClose}
          >
            Cancel
          </Button>
          <Button
            className="rounded-lg text-sm font-normal"
            onClick={onShare}
            disabled={progressStatus === ProgressStatus.LOADING}
            isLoading={progressStatus === ProgressStatus.LOADING}
          >
            Send
          </Button>
        </div>
      </>
    </Modal>
  );
};

export default EmailHomeownersModal;
