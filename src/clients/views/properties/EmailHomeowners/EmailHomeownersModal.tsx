'use client';

import { searchContacts } from '@/app/actions/profile';
import { createContactComment, shareListing } from '@/app/actions/property';
import Autocomplete, { AutocompleteOption } from '@/clients/ui/autocomplete';
import Button from '@/clients/ui/button';
import Checkbox from '@/clients/ui/checkbox';
import Input from '@/clients/ui/input';
import Modal from '@/clients/ui/modal';
import Textarea from '@/clients/ui/textarea';
import { EMAIL_PATTERN } from '@/constants/pattern';
import useForm from '@/hooks/useForm';
import { ProgressStatus } from '@/types/common';
import { Contact } from '@/types/profile';
import { XMarkIcon } from '@heroicons/react/24/outline';
import { useCallback, useMemo, useState } from 'react';
import toast from 'react-hot-toast';

type Props = {
  onClose: () => void;
  propertyIds: number[];
};

type FormValues = {
  other_email: string;
  agent_comments: string;
};

const EmailHomeownersModal = ({ onClose, propertyIds }: Props) => {
  const [text, setText] = useState<string>('');
  const [isFetching, setIsFetching] = useState<boolean>(false);
  const [contacts, setContacts] = useState<Contact[]>([]);
  const [selected, setSelected] = useState<Contact[]>([]);
  const [checked, setChecked] = useState<number[]>([]);
  const [progressStatus, setProgressStatus] = useState<ProgressStatus | null>(
    null
  );
  const [someoneElse, setSomeoneElse] = useState<boolean>(false);
  const {
    formState,
    pristine,
    errors,
    onChange,
    preSubmitCheck: preSubmitCheck,
  } = useForm<FormValues>(
    {
      other_email: '',
      agent_comments: '',
    },
    {
      other_email: (_v, _n, _value) => {
        if (someoneElse && _value.trim().length === 0) {
          return `Email is required.`;
        }

        if (someoneElse && !_value.match(EMAIL_PATTERN)) {
          return 'Enter valid email';
        }
      },
      agent_comments: (_v, _n, _value) => {
        if (_value.trim().length === 0) {
          return `Message is required.`;
        }
      },
    }
  );

  const options = useMemo(
    () =>
      contacts.map((_contact, index) => ({
        id: _contact.contact_id,
        label: (
          <span className="font-sm">
            {_contact.first_name} {_contact.last_name}{' '}
            <span className="font-xs text-disabled">({_contact.email1})</span>
          </span>
        ),
        value: _contact.contact_id,
      })),
    [contacts]
  );

  const shareUrl = useMemo(() => {
    let url = '';
    const params = new URLSearchParams();
    params.append('listings', propertyIds.join(','));
    const encodedQueryParams = Buffer.from(
      params.toString(),
      'binary'
    ).toString('base64');
    if (!!process && process.env.NODE_ENV === 'development') {
      url = `https://dev.booknantucket.com/share-listings?url=${encodedQueryParams}`;
    } else {
      url = `https://www.congdonandcoleman.com/share-listings?url=${encodedQueryParams}`;
    }
    return url;
  }, [propertyIds]);

  const handleCopyLink = useCallback(async () => {
    if (shareUrl) {
      await navigator.clipboard.writeText(shareUrl);
      toast('Link copied to clibboard', {
        position: 'bottom-center',
        style: {
          borderRadius: '10px',
          background: '#333',
          color: '#fff',
          fontSize: '14px',
        },
      });
    }
  }, [shareUrl]);

  const onChangeTextInput = useCallback(
    (event: any) => {
      const { name, value } = event.target;

      onChange(value, name);
    },
    [onChange]
  );

  const onChangeTableCheckbox = useCallback(
    (event: any, id: number) => {
      const { name, checked: checkedValue } = event.target;
      if (!checkedValue) {
        setChecked(checked.filter((_c) => _c !== id));
        setSelected(selected.filter((_s) => _s.contact_id !== id));
      }
    },
    [checked, selected]
  );

  const onSelectContact = useCallback(
    (_option: AutocompleteOption) => {
      if (selected.find((_s) => _s.contact_id === _option.id)) {
        setSelected(selected.filter((_c) => _c.contact_id !== _option.id));
        setChecked(checked.filter((_c) => _c !== _option.id));
      } else {
        const contact = contacts.find((_c) => _c.contact_id === _option.id);
        if (contact) {
          setSelected([...selected, contact]);
          setChecked([...checked, _option.id]);
        }
      }
    },
    [checked, contacts, selected]
  );

  const fetchContacts = useCallback((query = '') => {
    setIsFetching(true);
    searchContacts<{ results: Contact[] }>(query, ['buyer', 'seller', 'tenant'])
      .then(({ results }) => {
        setContacts(results);
        setIsFetching(false);
      })
      .catch((err) => {
        console.error(err);
        setIsFetching(false);
      });
  }, []);

  const createContactNote = useCallback(() => {
    const promises = checked.map((id) => {
      return createContactComment(id, {
        content: `Link to share listings: ${shareUrl}`,
      });
    });

    Promise.all(promises)
      .then((values) => {
        console.log(values);
        setProgressStatus(ProgressStatus.SUCCESSFUL);
        toast.success('Listing Successfully shared!');
        onClose();
      })
      .catch((error) => {
        console.error(error.message);
        toast.success('failed!');
        setProgressStatus(ProgressStatus.FAILED);
      });
  }, [checked, onClose, shareUrl]);

  const onShare = useCallback(() => {
    const _errors = preSubmitCheck();
    if (Object.values(_errors).some((_error) => _error !== '')) {
      return;
    }

    if (propertyIds.length === 0) {
      toast.error('Please select at least one listing to share');
      return;
    }

    setProgressStatus(ProgressStatus.LOADING);
    console.log('checked', checked, selected);
    shareListing({
      listings: propertyIds,
      recipents: checked,
      other_email: someoneElse ? formState.other_email : '',
      arrival_date: null,
      departure_date: null,
      agent_comments: formState.agent_comments,
    })
      .then((data) => {
        console.debug('data is', data);
        createContactNote();
      })
      .catch((error) => {
        console.log('errror is', error);
        setProgressStatus(ProgressStatus.FAILED);
      });
  }, [
    checked,
    createContactNote,
    formState.agent_comments,
    formState.other_email,
    preSubmitCheck,
    propertyIds,
    selected,
    someoneElse,
  ]);

  return (
    <Modal open className="w-full md:w-[620px] p-6 !rounded-md">
      <>
        <div className="flex items-center justify-between">
          <p className="md:text-[19px] font-semibold">
            Select contacts to share listings
          </p>
          <XMarkIcon className="cursor-pointer w-4 h-4" onClick={onClose} />
        </div>
        <hr className="mt-2 mb-4" />
        <Autocomplete
          className="mb-2 text-sm p-2"
          dropdownClassName="absolute"
          value={text}
          placeholder="Search by name, email"
          options={options}
          onChangeValue={(text: string) => setText(text)}
          isFetchingData={isFetching}
          fetchData={fetchContacts}
          onSelect={onSelectContact}
        />
        {selected.length > 0 && (
          <div className="overflow-x-auto">
            <table className="table table-zebra text-[10px] md:text-xs">
              <thead className="bg-white">
                <tr>
                  <th className="text-black-60 font-normal w-[10%]"></th>
                  <th className="text-black-60 font-normal w-[40%]">Name</th>
                  <th className="text-black-60 font-normal w-[50%]">Email</th>
                </tr>
              </thead>
              <tbody>
                {selected?.map((_c, index) => (
                  <tr key={index}>
                    <td className="px-0">
                      <Checkbox
                        className="w-4 h-4"
                        checked={checked.includes(_c.contact_id)}
                        onChange={(e) =>
                          onChangeTableCheckbox(e, _c.contact_id)
                        }
                      />
                    </td>
                    <td className="px-0">
                      {_c.first_name} {_c.last_name}
                    </td>
                    <td className="px-0">{_c.email1}</td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
        )}

        <div className="flex items-center gap-2 my-2">
          <Checkbox
            className="w-4 h-4"
            checked={someoneElse}
            onChange={() => setSomeoneElse(true)}
          />
          <p className="flex-grow text-xs">Send to someone else</p>
        </div>
        {someoneElse && (
          <Input
            name="other_email"
            className="text-sm p-2 w-full md:w-[60%] my-2"
            placeholder="Enter email adress"
            onChange={onChangeTextInput}
            value={formState.other_email}
            helperText={errors?.other_email ?? ''}
            error={!!errors?.other_email?.length}
          />
        )}
        <p className="text-sm font-medium mt-4 text-metal-gray">
          Click the following to copy a shareable link :
        </p>
        <p
          onClick={handleCopyLink}
          className="text-sm leading-5 cursor-pointer mb-4"
        >
          {shareUrl}
        </p>
        <p className="text-sm font-medium text-metal-gray">
          Enter a message to the recipient：
        </p>
        <Textarea
          name="agent_comments"
          rows={3}
          className="w-full my-2 text-sm"
          placeholder="Message to share..."
          onChange={onChangeTextInput}
          value={formState.agent_comments}
          helperText={errors?.agent_comments ?? ''}
          error={!!errors?.agent_comments?.length}
        />
        <div className="w-full flex items-center justify-between mt-4">
          <Button
            intent="secondary"
            className="rounded-lg text-sm font-normal"
            onClick={onClose}
          >
            Cancel
          </Button>
          <Button
            className="rounded-lg text-sm font-normal"
            onClick={onShare}
            disabled={progressStatus === ProgressStatus.LOADING}
            isLoading={progressStatus === ProgressStatus.LOADING}
          >
            Share
          </Button>
        </div>
      </>
    </Modal>
  );
};

export default EmailHomeownersModal;
