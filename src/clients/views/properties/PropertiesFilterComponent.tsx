'use client';

import Button from '@/clients/ui/button';
import { useCallback, useState, useEffect, useContext } from 'react';
import RentFilter from './Filters/RentFilter';
import { Nullable } from '@/types/common';
import Image from 'next/image';
import { PropertyArea } from '@/types/property';
import { useRouter, useSearchParams, usePathname } from 'next/navigation';
import DateRangePicker from '@/clients/components/common/DateRangePicker';
import { parseDateString } from '@/utils/common';
import { format, isValid } from 'date-fns';
import { CalendarDaysIcon } from '@heroicons/react/24/outline';
import { DateRange } from 'react-day-picker';
import useSWR from 'swr';
import { getAreas } from '@/app/actions/property';
import MultiSelect from '@/clients/ui/multi-select';
import AmenitiesFilter from './Filters/AmenitiesFilter';
import NRListingIDFilter from './Filters/NRListingIDFilter';
import Select from '@/clients/ui/select';

import OwnerNameFilter from './Filters/OwnerNameFilter';
import KeyNumberFilter from './Filters/KeyNumberFilter';
import DatePicker from '@/clients/components/common/DatePicker';
import { ListingsContext } from '@/clients/contexts/ListingsContext';
import DateRangePickerMobile from '@/clients/components/common/DateRangePicker/DateRangePickerMobile';
import Input from '@/clients/ui/input';

const BASE_URL = process.env.NEXT_PUBLIC_BASE_URL || '';

export const SELECT_OPTIONS = Array(100)
  .fill(1)
  .map((_, index) => ({
    id: (index + 1).toString(),
    name: (index + 1).toString(),
  }));

type FormState = {
  min_price: Nullable<number>;
  max_price: Nullable<number>;
  address: string;
  dates: [Nullable<string>, Nullable<string>];
  area: number[];
  bedroom_num_gte: Nullable<string>;
  air_conditioning_central: boolean;
  air_conditioning_minisplit: boolean;
  air_conditioning_windowunits: boolean;
  pets_askowner: boolean;
  pets_yes: boolean;
  pets_no: boolean;
  pool_private: boolean;
  pool_community: boolean;
  walk_to_beach: boolean;
  waterfront: boolean;
  water_views: boolean;
  nr_listing_id: string;
  key_number: Nullable<string>;
  owner_name: string;
  nights: Nullable<string>;
  capacity_gte: Nullable<string>;
  last_update: Nullable<string>;
  show_inactive: boolean;
  show_without_price: boolean;
};

const PropertiesFilterComponent = ({ onClose }: { onClose?: () => void }) => {
  const router = useRouter();
  const searchParams = useSearchParams();
  const pathname = usePathname();
  const { data: areasPyload, isLoading } = useSWR('areas', () =>
    getAreas<{ results: PropertyArea[] }>()
  );

  const { setIsUpdatingSearch } = useContext(ListingsContext);

  // Form state
  const [formData, setFormData] = useState<FormState>({
    dates: [
      searchParams.get('start_date') ?? null,
      searchParams.get('end_date') ?? null,
    ],
    last_update: searchParams?.get('last_update') ?? null,
    area: searchParams.getAll('area')
      ? [...searchParams.getAll('area').map((_s) => parseInt(_s))]
      : [],
    key_number: searchParams.get('key_number') ?? '',
    owner_name: searchParams.get('owner_name') ?? '',
    nr_listing_id: searchParams.get('nr_listing_id') ?? '',
    nights: searchParams.get('nights') ?? null,
    capacity_gte: searchParams.get('capacity_gte') ?? null,
    min_price: searchParams.get('min_price')
      ? parseInt(searchParams.get('min_price') ?? '0')
      : null,
    max_price: searchParams.get('max_price')
      ? parseInt(searchParams.get('max_price') ?? '0')
      : null,
    address: searchParams.get('address') ?? '',
    show_inactive: !!searchParams?.get('show_inactive'),
    show_without_price: !!searchParams?.get('show_without_price'),
    bedroom_num_gte: searchParams.get('bedroom_num_gte') ?? null,
    air_conditioning_central: !!(
      searchParams.get('ac_type') &&
      searchParams.getAll('ac_type').includes('1')
    ),
    air_conditioning_minisplit: !!(
      searchParams.get('ac_type') &&
      searchParams.getAll('ac_type').includes('2')
    ),
    air_conditioning_windowunits: !!(
      searchParams.get('ac_type') &&
      searchParams.getAll('ac_type').includes('3')
    ),
    pets_askowner: !!(
      searchParams.get('pet_allow') &&
      searchParams.getAll('pet_allow').includes('ask_owner')
    ),
    pets_yes: !!(
      searchParams.get('pet_allow') &&
      searchParams.getAll('pet_allow').includes('true')
    ),
    pets_no: !!(
      searchParams.get('pet_allow') &&
      searchParams.getAll('pet_allow').includes('false')
    ),
    pool_private: !!(
      searchParams.get('pool_type') &&
      searchParams.getAll('pool_type').includes('2')
    ),
    pool_community: !!(
      searchParams.get('pool_type') &&
      searchParams.getAll('pool_type').includes('1')
    ),
    walk_to_beach: !!(
      searchParams.get('walk_to_beach') &&
      searchParams.getAll('walk_to_beach').includes('true')
    ),
    waterfront: !!(
      searchParams.get('waterfront') &&
      searchParams.getAll('waterfront').includes('true')
    ),
    water_views: !!(
      searchParams.get('water_views') &&
      searchParams.getAll('water_views').includes('true')
    ),
  });

  const onChangeRent = useCallback(
    (min: Nullable<number>, max: Nullable<number>) => {
      setIsUpdatingSearch(true);
      setFormData((_f) => ({
        ..._f,
        min_price: min,
        max_price: max,
      }));

      // Create new URLSearchParams from current search params
      const params = new URLSearchParams(searchParams.toString());

      // Update or add the min_price parameter
      if (min !== null) {
        params.set('min_price', min.toString());
      } else {
        params.delete('min_price');
      }

      // Update or add the max_price parameter
      if (max !== null) {
        params.set('max_price', max.toString());
      } else {
        params.delete('max_price');
      }

      // Navigate to the updated URL
      router.push(`${pathname}?${params.toString()}`);
    },
    [router, searchParams, pathname, setIsUpdatingSearch]
  );

  const onChangeAddress = useCallback(
    (e: React.ChangeEvent<HTMLInputElement>) => {
      setFormData((_f) => ({ ..._f, address: e.target.value }));
    },
    []
  );

  const onKeyDownAddress = useCallback(
    (e: any) => {
      console.log({
        enter: e.key === 'Enter',
        value: e.target.value,
      });
      if (e.key === 'Enter') {
        setIsUpdatingSearch(true);
        // Create new URLSearchParams from current search params
        const params = new URLSearchParams(searchParams.toString());

        // Update or add the min_price parameter
        if (e.target.value !== '') {
          params.set('address', e.target.value);
        } else {
          params.delete('address');
        }

        // Navigate to the updated URL
        router.push(`${pathname}?${params.toString()}`);
      }
    },
    [pathname, router, searchParams, setIsUpdatingSearch]
  );

  const onChangeDateRange = useCallback(
    (_d?: DateRange) => {
      setFormData((_f) => ({
        ..._f,
        dates: [
          _d?.from ? format(_d?.from, 'yyyy-MM-dd') : null,
          _d?.to ? format(_d?.to, 'yyyy-MM-dd') : null,
        ],
      }));
      const params = new URLSearchParams(searchParams.toString());
      params.delete('start_date');
      params.delete('end_date');
      if (_d?.from && _d?.to) {
        setIsUpdatingSearch(true);
        params.set('start_date', format(_d?.from, 'yyyy-MM-dd'));
        params.set('end_date', format(_d?.to, 'yyyy-MM-dd'));
        router.push(`${pathname}?${params.toString()}`);
      }

      if (!_d?.from && !_d?.to) {
        setIsUpdatingSearch(true);
        router.push(`${pathname}?${params.toString()}`);
      }
    },
    [pathname, router, searchParams, setIsUpdatingSearch]
  );

  const onChangeNights = useCallback(
    (value: any) => {
      setIsUpdatingSearch(true);
      setFormData((_f) => ({
        ..._f,
        nights: value.id,
      }));
      // Create new URLSearchParams from current search params
      const params = new URLSearchParams(searchParams.toString());

      // Update or add the min_price parameter
      if (value.id) {
        params.set('nights', value.id);
      } else {
        params.delete('nights');
      }

      // Navigate to the updated URL
      router.push(`${pathname}?${params.toString()}`);
    },
    [pathname, router, searchParams, setIsUpdatingSearch]
  );

  const onChangeBeds = useCallback(
    (value: any) => {
      setIsUpdatingSearch(true);
      setFormData((_f) => ({
        ..._f,
        bedroom_num_gte: value.id,
      }));
      // Create new URLSearchParams from current search params
      const params = new URLSearchParams(searchParams.toString());

      // Update or add the min_price parameter
      if (value.id) {
        params.set('bedroom_num_gte', value.id);
      } else {
        params.delete('bedroom_num_gte');
      }

      // Navigate to the updated URL
      router.push(`${pathname}?${params.toString()}`);
    },
    [pathname, router, searchParams, setIsUpdatingSearch]
  );

  const onChangeGuests = useCallback(
    (value: any) => {
      setIsUpdatingSearch(true);
      setFormData((_f) => ({
        ..._f,
        capacity_gte: value.id,
      }));
      // Create new URLSearchParams from current search params
      const params = new URLSearchParams(searchParams.toString());

      // Update or add the min_price parameter
      if (value.id) {
        params.set('capacity_gte', value.id);
      } else {
        params.delete('capacity_gte');
      }

      // Navigate to the updated URL
      router.push(`${pathname}?${params.toString()}`);
    },
    [pathname, router, searchParams, setIsUpdatingSearch]
  );

  const onChangeArea = useCallback(
    (_a: { id: any; name: string }) => {
      setIsUpdatingSearch(true);
      const area = formData.area.includes(_a.id)
        ? formData.area.filter((areaNumber) => areaNumber !== _a.id)
        : [...formData.area, _a.id as number];
      setFormData((_f) => ({ ..._f, area }));

      const params = new URLSearchParams(searchParams.toString());

      params.delete('area');
      if (area.length > 0) {
        area.map((area) => {
          params.append('area', area.toString());
        });
      }

      // Navigate to the updated URL
      router.push(`${pathname}?${params.toString()}`);
    },
    [formData.area, pathname, router, searchParams, setIsUpdatingSearch]
  );

  const onChangeLastUpdate = useCallback(
    (dt?: Date) => {
      if (dt) {
        setIsUpdatingSearch(true);
        setFormData((_f) => ({ ..._f, last_update: format(dt, 'yyyy-MM-dd') }));
      }

      const params = new URLSearchParams(searchParams.toString());

      if (dt) {
        params.set('last_update', format(dt, 'yyyy-MM-dd'));
      } else {
        params.delete('last_update');
      }

      // Navigate to the updated URL
      router.push(`${pathname}?${params.toString()}`);
    },
    [pathname, router, searchParams, setIsUpdatingSearch]
  );

  const onRemoveArea = useCallback(
    (id: any) => {
      setIsUpdatingSearch(true);
      const area = formData.area?.filter((_t) => _t !== id);
      setFormData({
        ...formData,
        area,
      });

      const params = new URLSearchParams(searchParams.toString());

      params.delete('area');
      if (area.length > 0) {
        area.map((area) => {
          params.append('area', area.toString());
        });
      }

      // Navigate to the updated URL
      router.push(`${pathname}?${params.toString()}`);
    },
    [formData, pathname, router, searchParams, setIsUpdatingSearch]
  );

  const onClearFilters = useCallback(() => {
    setIsUpdatingSearch(true);
    router.push(
      `${pathname}?ordering=not_renting_year,priority,-calendar_updated_at&show_price=true`
    );
    setFormData({
      dates: [null, null],
      address: '',
      min_price: null,
      max_price: null,
      area: [],
      air_conditioning_central: false,
      air_conditioning_minisplit: false,
      air_conditioning_windowunits: false,
      pets_askowner: false,
      pets_yes: false,
      pets_no: false,
      pool_private: false,
      pool_community: false,
      walk_to_beach: false,
      waterfront: false,
      water_views: false,
      nr_listing_id: '',
      key_number: null,
      owner_name: '',
      nights: null,
      capacity_gte: null,
      bedroom_num_gte: null,
      last_update: null,
      show_inactive: false,
      show_without_price: false,
    });
    onClose?.();
  }, [onClose, pathname, router, setIsUpdatingSearch]);

  // Reset loading state when search parameters change
  useEffect(() => {
    setIsUpdatingSearch(false);
  }, [searchParams, setIsUpdatingSearch]);

  return (
    <div className="flex flex-col md:flex-row gap-2 max-w-full overflow-x-auto no-scrollbar">
      <div className="md:min-w-[220px] md:w-[30%]">
        <RentFilter
          minPrice={formData.min_price}
          maxPrice={formData.max_price}
          onChange={onChangeRent}
          max={30000}
        />
        <Input
          icon={
            <Image
              alt="rent filter icon"
              src="/images/icons/street.svg"
              width={20}
              height={20}
              className="absolute left-2 z-10"
            />
          }
          placeholder="Street Address"
          className="text-sm !p-2 w-full h-[42px] !pl-8"
          wrapperclassName="w-full flex items-center my-2"
          value={formData.address}
          onChange={onChangeAddress}
          onKeyDown={onKeyDownAddress}
        />
      </div>
      <div className="md:min-w-[220px] md:w-[30%]">
        <div className="hidden md:block w-full">
          <DateRangePicker
            date={{
              from: formData.dates[0]
                ? parseDateString(formData.dates[0])
                : undefined,
              to: formData.dates[1]
                ? parseDateString(formData.dates[1])
                : undefined,
            }}
            setDate={onChangeDateRange}
            className="border rounded h-[42px]"
            enableAllDates
            title={
              <div className="text-sm text-roman-silver flex items-center gap-x-2">
                <CalendarDaysIcon className="w-5 h-5" />
                <span className="truncate">
                  {formData.dates.every((_d) =>
                    isValid(parseDateString(_d ?? ''))
                  )
                    ? `${format(
                        formData.dates[0] ?? '',
                        'M/d/yyyy'
                      )} - ${format(formData.dates[1] ?? '', 'M/d/yyyy')}`
                    : `Date Range`}
                </span>
              </div>
            }
            closeOnClear
          />
        </div>
        <div className="md:hidden w-full">
          <DateRangePickerMobile
            date={{
              from: formData.dates[0]
                ? parseDateString(formData.dates[0])
                : undefined,
              to: formData.dates[1]
                ? parseDateString(formData.dates[1])
                : undefined,
            }}
            setDate={onChangeDateRange}
            className="border rounded h-[42px]"
            title={
              <div className="text-sm text-roman-silver flex items-center gap-x-2">
                <CalendarDaysIcon className="w-5 h-5" />
                <span className="truncate">
                  {formData.dates.every((_d) =>
                    isValid(parseDateString(_d ?? ''))
                  )
                    ? `${format(
                        formData.dates[0] ?? '',
                        'M/d/yyyy'
                      )} - ${format(formData.dates[1] ?? '', 'M/d/yyyy')}`
                    : `Date Range`}
                </span>
              </div>
            }
          />
        </div>
        <MultiSelect
          className="text-sm w-full px-2 py-2 my-2 h-[42px]"
          bodyClassName="max-h-[200px] overflow-y-scroll z-[9999]"
          placeholder={
            <>
              <Image
                alt="rent filter icon"
                src="/images/icons/street.svg"
                width={20}
                height={20}
                className="absolute left-2 z-10"
              />
              <p className="pl-6">Neighborhoods</p>
            </>
          }
          options={areasPyload?.results ?? []}
          value={formData?.area}
          onRemove={onRemoveArea}
          isClearable
          onChange={onChangeArea}
        />
      </div>
      <div className="md:min-w-[220px] md:w-[30%]">
        <AmenitiesFilter
          air_conditioning_central={formData.air_conditioning_central}
          air_conditioning_minisplit={formData.air_conditioning_minisplit}
          air_conditioning_windowunits={formData.air_conditioning_windowunits}
          pets_askowner={formData.pets_askowner}
          pets_yes={formData.pets_yes}
          pets_no={formData.pets_no}
          pool_private={formData.pool_private}
          pool_community={formData.pool_community}
          walk_to_beach={formData.walk_to_beach}
          waterfront={formData.waterfront}
          water_views={formData.water_views}
          setIsUpdatingSearch={setIsUpdatingSearch}
        />
        <NRListingIDFilter
          setIsUpdatingSearch={setIsUpdatingSearch}
          nr_listing_id={formData.nr_listing_id}
        />
      </div>
      <div className="md:min-w-[220px] md:w-[30%]">
        <div className="flex gap-x-2 h-[42px]">
          <Select
            className="text-sm w-1/2 px-2 py-2"
            bodyClassName="max-h-[200px] z-[9999] overflow-y-scroll"
            placeholder="Nights"
            options={SELECT_OPTIONS}
            value={formData.nights}
            onChange={onChangeNights}
            text={formData.nights ? `${formData.nights} nights` : undefined}
          />
          <Select
            className="text-sm w-1/2 px-2 py-2"
            bodyClassName="max-h-[200px] z-[9999] overflow-y-scroll"
            placeholder="Guests"
            options={SELECT_OPTIONS}
            value={formData.capacity_gte}
            onChange={onChangeGuests}
            text={
              formData.capacity_gte
                ? `${formData.capacity_gte} guests`
                : undefined
            }
          />
        </div>
        <OwnerNameFilter
          setIsUpdatingSearch={setIsUpdatingSearch}
          owner_name={formData.owner_name}
        />
      </div>
      <div className="md:min-w-[220px] md:w-[30%]">
        <div className="flex gap-x-2 h-[42px]">
          <Select
            className="text-sm w-1/2 px-2 py-2"
            bodyClassName="max-h-[200px] z-[9999] overflow-y-scroll"
            placeholder="Beds"
            options={SELECT_OPTIONS}
            value={formData.bedroom_num_gte}
            onChange={onChangeBeds}
            text={
              formData.bedroom_num_gte
                ? `${formData.bedroom_num_gte} beds`
                : undefined
            }
          />
          <KeyNumberFilter
            setIsUpdatingSearch={setIsUpdatingSearch}
            key_number={formData.key_number ?? ''}
          />
        </div>
        <DatePicker
          className="!p-2 rounded-md my-2 !text-sm h-[42px]"
          popOverClassName="p-0 w-full"
          placeholder={
            <span className="flex items-center gap-x-2 text-roman-silver">
              <CalendarDaysIcon className="w-5 h-5" />
              Last Update
            </span>
          }
          selected={
            formData.last_update
              ? parseDateString(formData.last_update)
              : undefined
          }
          onSelect={onChangeLastUpdate}
        />
      </div>
      <div className="md:min-w-[220px] md:w-[30%] flex flex-col items-center">
        <Button
          className="md:min-w-[220px] md:w-[30%] text-sm font-medium border-warning text-warning hover:text-warning/50 hover:border-warning/20 md:h-[42px]"
          intent="outline"
          onClick={onClearFilters}
        >
          Clear Filter
        </Button>
        <a
          href={`${BASE_URL}/property`}
          className="hidden md:block md:min-w-[220px] md:w-[30%] text-sm font-medium my-2 border border-[#092a35] hover:text-black/60 hover:border-black/60 px-4 py-2 rounded text-center w-full md:h-[42px]"
        >
          Add A Listing
        </a>
      </div>
    </div>
  );
};

export default PropertiesFilterComponent;
