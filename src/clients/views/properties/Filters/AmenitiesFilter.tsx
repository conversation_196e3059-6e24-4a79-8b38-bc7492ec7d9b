'use client';

import Button from '@/clients/ui/button';
import Checkbox from '@/clients/ui/checkbox';
import { ChevronDownIcon, ChevronUpIcon } from '@heroicons/react/24/outline';
import { Popover, PopoverContent, PopoverTrigger } from '@/clients/ui/popover';
import { usePathname, useRouter, useSearchParams } from 'next/navigation';
import { ChangeEvent, memo, useCallback, useState } from 'react';

type Props = {
  air_conditioning_central: boolean;
  air_conditioning_minisplit: boolean;
  air_conditioning_windowunits: boolean;
  pets_askowner: boolean;
  pets_yes: boolean;
  pets_no: boolean;
  pool_private: boolean;
  pool_community: boolean;
  walk_to_beach: boolean;
  waterfront: boolean;
  water_views: boolean;
  setIsUpdatingSearch: (s: boolean) => void;
};

const AmenitiesFilter = ({ setIsUpdatingSearch, ...props }: Props) => {
  const router = useRouter();
  const pathname = usePathname();
  const searchParams = useSearchParams();
  const [open, setOpen] = useState<boolean>(false);
  const [state, setState] = useState({
    air_conditioning_central: props.air_conditioning_central,
    air_conditioning_minisplit: props.air_conditioning_minisplit,
    air_conditioning_windowunits: props.air_conditioning_windowunits,
    pets_askowner: props.pets_askowner,
    pets_yes: props.pets_yes,
    pets_no: props.pets_no,
    pool_private: props.pool_private,
    pool_community: props.pool_community,
    walk_to_beach: props.walk_to_beach,
    waterfront: props.waterfront,
    water_views: props.water_views,
  });

  const onChangeCheckbox = useCallback((e: ChangeEvent<HTMLInputElement>) => {
    const { name, checked } = e.target;
    setState((_s) => ({
      ..._s,
      [name]: checked,
    }));
  }, []);

  const onDone = useCallback(() => {
    setOpen(false);
    if (Object.values(state).filter((_t) => _t).length === 0) {
      return;
    }
    setIsUpdatingSearch(true);
    const params = new URLSearchParams(searchParams.toString());
    params.delete('ac_type');
    params.delete('pet_allow');
    params.delete('pool_type');
    params.delete('walk_to_beach');
    params.delete('waterfront');
    params.delete('water_views');
    Object.entries(state).forEach(([k, v]) => {
      if (v) {
        if (k === 'air_conditioning_central') params.append('ac_type', '1');
        else if (k === 'air_conditioning_minisplit')
          params.append('ac_type', '2');
        else if (k === 'air_conditioning_windowunits')
          params.append('ac_type', '3');
        else if (k === 'pets_askowner') params.append('pet_allow', 'ask_owner');
        else if (k === 'pets_yes') params.append('pet_allow', 'true');
        else if (k === 'pets_no') params.append('pet_allow', 'false');
        else if (k === 'pool_private') params.append('pool_type', '2');
        else if (k === 'pool_community') params.append('pool_type', '1');
        else if (k === 'walk_to_beach') params.append('walk_to_beach', 'true');
        else if (k === 'waterfront') params.append('waterfront', 'true');
        else if (k === 'water_views') params.append('water_views', 'true');
      } else {
        params.delete(k);
      }
    });
    router.push(`${pathname}?${params.toString()}`);
  }, [pathname, router, searchParams, setIsUpdatingSearch, state]);

  return (
    <Popover open={open} onOpenChange={setOpen}>
      <PopoverTrigger asChild>
        <div className="p-2 border rounded flex items-center justify-between w-full cursor-pointer relative h-[42px]">
          <p className="text-sm">
            Amenities & Features{' '}
            {Object.values(state).filter((_t) => _t).length > 0 &&
              `(${Object.values(state).filter((_t) => _t).length})`}
          </p>
          {open ? (
            <ChevronUpIcon className="w-auto h-[14px]" />
          ) : (
            <ChevronDownIcon className="w-auto h-[14px]" />
          )}
        </div>
      </PopoverTrigger>
      <PopoverContent
        className="w-[calc(100vw-32px)] md:w-80 p-2 z-[9999]"
        align="start"
      >
        <div className="flex items-center gap-x-2 my-2">
          <Checkbox
            className="w-4 h-4"
            name="air_conditioning_central"
            checked={state.air_conditioning_central}
            onChange={onChangeCheckbox}
          />
          <label className="text-sm">A/C: Central</label>
        </div>
        <div className="flex items-center gap-x-2 my-2">
          <Checkbox
            className="w-4 h-4"
            name="air_conditioning_minisplit"
            onChange={onChangeCheckbox}
            checked={state.air_conditioning_minisplit}
          />
          <label className="text-sm">A/C: Mini Split</label>
        </div>
        <div className="flex items-center gap-x-2 my-2">
          <Checkbox
            className="w-4 h-4"
            name="air_conditioning_windowunits"
            checked={state.air_conditioning_windowunits}
            onChange={onChangeCheckbox}
          />
          <label className="text-sm">A/C: Window Units</label>
        </div>
        <div className="flex items-center gap-x-2 my-2">
          <Checkbox
            className="w-4 h-4"
            name="pets_yes"
            checked={state.pets_yes}
            onChange={onChangeCheckbox}
          />
          <label className="text-sm">Pets: Yes</label>
        </div>
        <div className="flex items-center gap-x-2 my-2">
          <Checkbox
            className="w-4 h-4"
            name="pets_no"
            checked={state.pets_no}
            onChange={onChangeCheckbox}
          />
          <label className="text-sm">Pets: No</label>
        </div>
        <div className="flex items-center gap-x-2 my-2">
          <Checkbox
            className="w-4 h-4"
            name="pets_askowner"
            checked={state.pets_askowner}
            onChange={onChangeCheckbox}
          />
          <label className="text-sm">Pets: Ask owner</label>
        </div>
        <div className="flex items-center gap-x-2 my-2">
          <Checkbox
            className="w-4 h-4"
            name="pool_private"
            checked={state.pool_private}
            onChange={onChangeCheckbox}
          />
          <label className="text-sm">Pool: Private</label>
        </div>
        <div className="flex items-center gap-x-2 my-2">
          <Checkbox
            className="w-4 h-4"
            name="pool_community"
            checked={state.pool_community}
            onChange={onChangeCheckbox}
          />
          <label className="text-sm">Pool: Community</label>
        </div>
        <div className="flex items-center gap-x-2 my-2">
          <Checkbox
            className="w-4 h-4"
            name="walk_to_beach"
            checked={state.walk_to_beach}
            onChange={onChangeCheckbox}
          />
          <label className="text-sm">Walk to the beach</label>
        </div>
        <div className="flex items-center gap-x-2 my-2">
          <Checkbox
            className="w-4 h-4"
            name="waterfront"
            checked={state.waterfront}
            onChange={onChangeCheckbox}
          />
          <label className="text-sm">Waterfront</label>
        </div>
        <div className="flex items-center gap-x-2 my-2">
          <Checkbox
            className="w-4 h-4"
            name="water_views"
            checked={state.water_views}
            onChange={onChangeCheckbox}
          />
          <label className="text-sm">Water views</label>
        </div>
        <Button className="w-full text-sm" onClick={onDone}>
          Done
        </Button>
      </PopoverContent>
    </Popover>
  );
};

export default memo(AmenitiesFilter);
