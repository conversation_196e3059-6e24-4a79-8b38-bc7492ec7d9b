'use client';

import Input from '@/clients/ui/input';
import { UserIcon } from '@heroicons/react/24/outline';
import Image from 'next/image';
import { usePathname, useRouter, useSearchParams } from 'next/navigation';
import { memo, useCallback, useState } from 'react';

type Props = {
  setIsUpdatingSearch: (s: boolean) => void;
  owner_name: string;
};

const OwnerNameFilter = ({ setIsUpdatingSearch, owner_name }: Props) => {
  const router = useRouter();
  const searchParams = useSearchParams();
  const pathname = usePathname();
  const [id, setId] = useState(owner_name);

  const handleChange = useCallback((e: React.ChangeEvent<HTMLInputElement>) => {
    setId(e.target.value);
  }, []);

  const onEnterPressed = useCallback(
    (e: any) => {
      if (e.key === 'Enter') {
        setIsUpdatingSearch(true);
        const params = new URLSearchParams(searchParams.toString());
        if (id === '') {
          params.delete('owner_name');
        } else params.set('owner_name', id);
        router.push(`${pathname}?${params.toString()}`);
      }
    },
    [id, pathname, router, searchParams, setIsUpdatingSearch]
  );

  return (
    <Input
      icon={<UserIcon className="w-5 h-5 absolute left-2 z-10" />}
      wrapperclassName="my-2 h-[42px] flex items-center"
      className="text-sm !p-2 w-full !pl-8 h-[42px]"
      placeholder="Owner Name"
      value={id}
      onChange={handleChange}
      onKeyDown={onEnterPressed}
    />
  );
};

export default memo(OwnerNameFilter);
