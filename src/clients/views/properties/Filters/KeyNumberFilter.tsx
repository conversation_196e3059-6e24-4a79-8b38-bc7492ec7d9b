'use client';

import Input from '@/clients/ui/input';
import Image from 'next/image';
import { usePathname, useRouter, useSearchParams } from 'next/navigation';
import { memo, useCallback, useState } from 'react';

type Props = {
  setIsUpdatingSearch: (s: boolean) => void;
  key_number: string;
};

const KeyNumberFilter = ({ setIsUpdatingSearch, key_number }: Props) => {
  const router = useRouter();
  const searchParams = useSearchParams();
  const pathname = usePathname();
  const [id, setId] = useState(key_number);

  const handleChange = useCallback((e: React.ChangeEvent<HTMLInputElement>) => {
    setId(e.target.value);
  }, []);

  const onEnterPressed = useCallback(
    (e: any) => {
      if (e.key === 'Enter') {
        setIsUpdatingSearch(true);
        const params = new URLSearchParams(searchParams.toString());
        if (id === '') {
          params.delete('key_number');
        } else params.set('key_number', id);
        router.push(`${pathname}?${params.toString()}`);
      }
    },
    [id, pathname, router, searchParams, setIsUpdatingSearch]
  );

  return (
    <Input
      icon={
        <Image
          alt="key icon"
          src="/images/icons/key.svg"
          width={20}
          height={20}
          className="absolute left-2 z-10"
        />
      }
      wrapperclassName="h-[42px] flex items-center"
      className="text-sm !p-2 !pl-8 w-full h-[42px]"
      placeholder="Key #"
      value={id}
      onChange={handleChange}
      onKeyDown={onEnterPressed}
    />
  );
};

export default memo(KeyNumberFilter);
