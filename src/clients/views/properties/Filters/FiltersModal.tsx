import PropertiesFilterComponent from '../PropertiesFilterComponent';
import ResponsiveDialog from '../../common/ResponsiveDialog';
import { XCircleIcon, XMarkIcon } from '@heroicons/react/24/outline';
import Button from '@/clients/ui/button';

type Props = {
  onClose: () => void;
};

const FiltersModal = ({ onClose }: Props) => {
  return (
    <ResponsiveDialog
      open
      dialogClassName="p-0 md:h-auto md:w-[650px]"
      drawerClassName="h-[78dvh] overflow-y-auto max-h-[85dvh]"
      drawerContentClassName="!p-0"
      hideCloseButton
    >
      <div className={`p-4`}>
        <Button
          onClick={onClose}
          intent="ghost"
          className="!p-0 absolute top-4 right-4"
        >
          <XCircleIcon className="w-6 h-6" />
        </Button>
        <p className="font-medium text-lg md:text-xl leading-[140%] tracking-[0.5px] m-0 text-center md:text-left">
          Listing filters
        </p>
        <hr className="my-4" />
        <PropertiesFilterComponent onClose={onClose} />
      </div>
    </ResponsiveDialog>
  );
};

export default FiltersModal;
