'use client';

import { AdjustmentsHorizontalIcon } from '@heroicons/react/24/outline';
import { useCallback, useState } from 'react';
import { PropertyImage } from '@/types/property';
import dynamic from 'next/dynamic';
import Button from '@/clients/ui/button';
import LoadingSpinner from '@/clients/ui/loading-spinner';

const FiltersModal = dynamic(() => import('./FiltersModal'), {
  ssr: false,
  loading: () => (
    <div className="fixed inset-0 bg-white/70 flex items-center justify-center z-[9999] rounded">
      <LoadingSpinner className="w-10 h-10 text-olive" />
    </div>
  ),
});

type Props = {
  photos: PropertyImage[];
  propertyId: number;
};
const FiltersButton = () => {
  const [show, setShow] = useState<boolean>(false);

  const onToggle = useCallback(() => {
    setShow(!show);
  }, [show]);

  return (
    <>
      <Button
        className="h-9 md:h-[42px] flex items-center gap-x-2"
        intent="outline"
        onClick={onToggle}
      >
        <AdjustmentsHorizontalIcon className="w-4 h-4" />
        <p className="hidden md:block text-xs font-medium">Filters</p>
      </Button>
      {show && <FiltersModal onClose={onToggle} />}
    </>
  );
};

export default FiltersButton;
