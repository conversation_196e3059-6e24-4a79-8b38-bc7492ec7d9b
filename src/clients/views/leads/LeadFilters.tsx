'use client';

import Checkbox from '@/clients/ui/checkbox';
import LoadingSpinner from '@/clients/ui/loading-spinner';
import { LeadStatus } from '@/types/leads';
import { usePathname, useRouter, useSearchParams } from 'next/navigation';
import { memo, useCallback, useEffect, useMemo, useState } from 'react';

type Props = {};

const LeadFilters = ({}) => {
  const searchParams = useSearchParams();
  const pathname = usePathname();
  const router = useRouter();
  const checked = useMemo(
    () => searchParams.get('status')?.includes(LeadStatus.CLOSED_LOST),
    [searchParams]
  );

  // UI state
  const [isUpdatingSearch, setIsUpdatingSearch] = useState(false);

  const onChangeCheckbox = useCallback(
    (event: any) => {
      const { checked } = event.target;
      const status = checked
        ? 'discussion%2Cnot_started%2Cclosed_lost'
        : 'discussion%2Cnot_started';

      const params = new URLSearchParams(searchParams.toString());
      setIsUpdatingSearch(true);
      params.set('status', status);
      router.push(`${pathname}?${params.toString()}`);
    },
    [pathname, router, searchParams]
  );

  // Reset loading state when search parameters change
  useEffect(() => {
    setIsUpdatingSearch(false);
  }, [searchParams]);

  return (
    <>
      {isUpdatingSearch && (
        <div className="absolute inset-0 bg-white/70 flex items-center justify-center z-[9999] rounded">
          <LoadingSpinner className="w-10 h-10 text-olive" />
        </div>
      )}

      <div className="flex items-center gap-x-2 my-2">
        <Checkbox
          className="w-4 h-4"
          name="show_inactive"
          checked={checked}
          onChange={onChangeCheckbox}
        />
        <label className="text-sm">Show Lost Leads</label>
      </div>
    </>
  );
};

export default memo(LeadFilters);
