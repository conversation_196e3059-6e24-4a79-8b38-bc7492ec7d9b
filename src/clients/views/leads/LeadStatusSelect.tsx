'use client';

import { updateLead } from '@/app/actions/leads';
import { revalidateTagByName } from '@/app/actions/revalidateTag';
import Select from '@/clients/ui/select';
import { LeadStatus } from '@/types/leads';
import classNames from 'classnames';
import { useSearchParams } from 'next/navigation';
import { useCallback, useState } from 'react';
import toast from 'react-hot-toast';

const LEAD_STATUS_OPTIONS = [
  { id: LeadStatus.NOT_STARTED, name: 'New' },
  { id: LeadStatus.DISCUSSION, name: 'Contacted' },
  { id: LeadStatus.CLOSED_LOST, name: 'Lost' },
];

type Props = {
  status: LeadStatus;
  leadId: number;
};

const LeadStatusSelect = ({ status, leadId }: Props) => {
  const params = useSearchParams();

  const [isSubmitting, setIsSubmitting] = useState<boolean>(false);

  const onChangeSelect = useCallback(
    (value: { id: string | number; name: string }) => {
      setIsSubmitting(true);
      updateLead(leadId, {
        status: value.id,
      })
        .then(() => {
          setIsSubmitting(false);
          toast.success('Lead status updated successfully');
          revalidateTagByName(
            `leads-${params.get('offset')}-${params.get('limit')}`
          );
        })
        .catch((err) => {
          console.log(err);
          setIsSubmitting(false);
          toast.error('Failed to update Lead status');
        });
    },
    [leadId, params]
  );

  return (
    <Select
      className={classNames(
        'text-xs rounded-md w-min max-w-[100px] px-2 py-2 gap-x-1.5',
        {
          'bg-emerald-100 text-slate-700': status === LeadStatus.NOT_STARTED,
          'bg-sky-50 text-sky-700':
            status === LeadStatus.DISCUSSION ||
            status === LeadStatus.CLOSED_WON ||
            status === LeadStatus.PROPOSAL_SENT,
          'bg-rose-50 text-rose-700': status === LeadStatus.CLOSED_LOST,
        }
      )}
      bodyClassName="max-h-[200px] overflow-y-scroll w-[100px]"
      name="status"
      options={LEAD_STATUS_OPTIONS}
      value={status}
      onChange={onChangeSelect}
      disabled={isSubmitting}
    />
  );
};

export default LeadStatusSelect;
