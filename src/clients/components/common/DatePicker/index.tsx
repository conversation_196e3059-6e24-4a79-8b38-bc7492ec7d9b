'use client';

import { ReactNode, useCallback, useState } from 'react';

import { ChevronDownIcon } from '@heroicons/react/24/outline';

import { Popover, PopoverContent, PopoverTrigger } from '@/clients/ui/popover';

import { format, isValid } from 'date-fns';
import { twMerge } from 'tailwind-merge';

import CustomDatePicker from './CustomDatePicker';

type Props = {
  className?: string;
  placeholder?: string | ReactNode;
  value?: any;
  popOverClassName?: string;
  selected?: Date;
  onSelect: (dt?: Date) => void;
  dateFormat?: string;
  disabled?: boolean;
  minDate?: Date;
  maxDate?: Date;
};

const DatePicker = ({
  className = '',
  placeholder = 'Select date',
  popOverClassName,
  selected,
  onSelect,
  dateFormat = 'LLL d, yyyy',
  disabled = false,
  minDate,
  maxDate,
}: Props) => {
  const [open, setOpen] = useState<boolean>(false);

  const handleCalendarIconClick = useCallback(() => {
    if (!disabled) {
      setOpen(true);
    }
  }, [disabled]);

  const onSelectDate = useCallback(
    (date?: Date) => {
      onSelect(date);
      setOpen(false);
    },
    [onSelect]
  );

  return (
    <div className="relative">
      <Popover open={open} onOpenChange={disabled ? undefined : setOpen}>
        <PopoverTrigger
          asChild
          onClick={disabled ? undefined : handleCalendarIconClick}
        >
          <div
            className={twMerge(
              'relative border border-solid',
              disabled ? 'opacity-70' : 'cursor-pointer',
              className
            )}
          >
            {selected && isValid(selected)
              ? format(selected, dateFormat)
              : placeholder}
            <div className="absolute right-3 top-1/2 -translate-y-1/2">
              <ChevronDownIcon className="w-4 h-4 text-platinum" />
            </div>
          </div>
        </PopoverTrigger>

        {open && !disabled && (
          <PopoverContent
            className={twMerge(
              'w-max border border-solid border-english-manor border-opacity-40 p-6 z-[9999]',
              popOverClassName
            )}
            align="center"
          >
            <CustomDatePicker
              selected={selected}
              onSelect={onSelectDate}
              minDate={minDate}
              maxDate={maxDate}
            />
          </PopoverContent>
        )}
      </Popover>
    </div>
  );
};

export default DatePicker;
