import { AvailabilityType } from '@/types/calendar';
import { Nullable } from '@/types/common';

export const getAvailabilityText = (status: Nullable<AvailabilityType>) => {
  if (status === AvailabilityType.AVAILABLE) {
    return 'Available';
  }

  if (status === AvailabilityType.PENDING) {
    return 'Pending';
  }

  if (status === AvailabilityType.UNAVAILABLE) {
    return 'Leased';
  }

  return 'Available';
};

export const getOffsetFromNextLink = (nextLink: string) => {
  const parsed = new URL(nextLink);
  const offset = parsed.searchParams.get('offset');
  return offset;
};
