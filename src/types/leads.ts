import { Nullable } from './common';

export enum LeadStatus {
  DISCUSSION = 'discussion',
  NOT_STARTED = 'not_started',
  PROPOSAL_SENT = 'proposal_sent',
  CLOSED_WON = 'closed_won',
  CLOSED_LOST = 'closed_lost',
}

export type Lead = {
  id: number;
  contact: {
    contact_id: number;
    first_name: string;
    last_name: string;
    email1: string;
    cell_phone: Nullable<string>;
  };
  status_label: string;
  previous_rental: string;
  status: LeadStatus;
  assignment_status: string;
  flexibility: Nullable<string>;
  source: string;
  guest: number;
  children: number;
  arrival_date: Nullable<string>;
  departure_date: Nullable<string>;
  future_date: Nullable<string>;
  note: Nullable<string>;
  listing: Nullable<string>;
  lost_reason: Nullable<string>;
  created_at: string;
};
